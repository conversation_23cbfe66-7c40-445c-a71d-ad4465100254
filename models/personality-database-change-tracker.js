const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const personalityDatabaseChangeTrackerSchema = new mongoose.Schema({
  _id: { type: String, default: 'singleton' },
  highestCategoryId: { type: Number, default: 0 },
  highestSubcategoryId: { type: Number, default: 0 },
  highestProfileId: { type: Number, default: 0 },
  profilesChangesDetectedAt: { type: Date },
  categoriesChangesDetectedAt: { type: Date },
  subcategoriesChangesDetectedAt: { type: Date },
  lastUpdateAt: { type: Date, default: Date.now },
});

let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('PersonalityDatabaseChangeTracker', personalityDatabaseChangeTrackerSchema);