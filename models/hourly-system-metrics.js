const { DateTime } = require('luxon');
const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema(
  {
    date: { type: Date, required: true, unique: true },
    metrics: {
      numNewConnections: { type: Number, default: 0 },
    },
  },
  {
    versionKey: false,
  }
);

schema.index({ date: -1 });

schema.statics.increment = async function (metric, n = 1) {
  if (!metric) return;
  const date = DateTime.utc().set({ minute: 0, second: 0, millisecond: 0 }).toJSDate();

  const update = {
    $inc: {
      [`metrics.${metric}`]: n,
    },
  };

  await this.updateOne(
    { date: date },
    update,
    { upsert: true },
  );
}

schema.statics.getNewConnectionsLastNDays = async function (metric, days = 7) {
  if (!metric) return 0;
  const nDaysAgo = DateTime.utc().minus({ days }).toJSDate();

  const result = await this.aggregate([
    {
      $match: { 
        date: { 
          $gte: nDaysAgo, 
        } 
      } 
    }, 
    {
      $group: 
      { 
        _id: null, 
        totalCount: { 
          $sum: { 
            $ifNull: [`$metrics.${metric}`, 0]
          } 
        } 
      } 
    }
  ]);

  return result.length ? result[0].totalCount : 0;
};


const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('HourlyMetrics', schema);
