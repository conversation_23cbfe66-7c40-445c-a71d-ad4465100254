const mongoose = require('mongoose');
const Notification = require('./notification');
const s3 = require('../lib/s3');
const { languageCodes } = require('../lib/languages');
const { contiguous_keywordsV2, contiguous_text, extractValidWords } = require('../lib/text-search');
const Interest = require('./interest');
const { getLinkedCategories, getLinkedSubcategories, getLinkedProfiles } = require('../lib/database');
const { findLinkedPillarKeywords } = require('../lib/pillar-keywords');
const { findLinkedExploreKeywords } = require('../lib/explore-keywords');
const { excludedUniverseLinkingKeyWords } =require('../lib/exclude-from-universe-programmatic-linking')

const { validMbti } = require('../lib/personality');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');

let hideSingleCommentAndChildrenAfterBanDelete = null;

const commentSchema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  createdBy: { type: String, ref: 'User' },
  question: { type: mongoose.Schema.Types.ObjectId, ref: 'Question' },
  profile: { type: mongoose.Schema.Types.ObjectId, ref: 'Profile' },
  webPage: { type: mongoose.Schema.Types.ObjectId, ref: 'WebPage' },
  text: {
    type: String,
    default: '',
    trim: true,
    maxlength: 10000,
  },
  keywords: [{ type: String }],
  linkedKeywords: [{ type: String }],
  linkedExploreKeywords: [{ type: String }],
  linkedPillarKeywords: [{
    _id: false,
    keyword: { type: String },
    url: { type: String }
  }],
  linkedCategories: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
  }],
  linkedSubcategories: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
    categoryId: { type: Number },
  }],
  linkedProfiles: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
  }],
  vote: {
    type: {
      _id: false,
      mbti: { type: String, enum: validMbti },
      enneagram: { type: String, enum: enneagrams },
      horoscope: { type: String, enum: horoscopes },
    },
    default: undefined,
  },
  image: { type: String },
  isVideo: { type: Boolean },
  aspectRatio: { type: Number },
  audio: { type: String },
  audioWaveform: { type: [{ type: Number }], default: undefined },
  audioDuration: { type: Number },
  audioTranscription: { type: String, trim: true, default: undefined },
  gif: { type: String },
  parent: { type: mongoose.Schema.Types.ObjectId },
  repliedTo: { type: String, ref: 'User' },
  repliedToIsAnonymous: { type: Boolean },
  postRepliedTo: { type: mongoose.Schema.Types.ObjectId },
  depth: { type: Number },
  usersThatCommented: [{ type: String, ref: 'User' }],
  anonymousUsersThatCommented: [{ type: String }],
  numUsersThatCommented: { type: Number },
  numComments: { type: Number },
  numHiddenComments: { type: Number },
  numDeletedComments: { type: Number },
  usersThatLiked: [{ type: String, ref: 'User' }],
  usersThatLikedNotified: [{ type: String, ref: 'User' }],
  numLikes: { type: Number },
  usersThatReported: [{ type: String, ref: 'User' }],
  isDeleted: { type: Boolean },
  isEdited: { type: Boolean },
  isHidden: { type: Boolean, default: false },
  flagged: { type: Boolean },
  flaggedByHive: { type: Boolean },
  flaggedByOpenai: { type: Boolean },
  hiveClass: { type: String },
  hiveScore: { type: Number },
  firstLikeAwarded: { type: Boolean },
  banned: { type: Boolean },
  bannedBy: { type: String, ref: 'User' },
  bannedDate: { type: Date },
  bannedReason: { type: String },
  bannedReasons: { type: [{ type: String }], default: undefined },
  bannedNotes: { type: String, default: undefined },
  bannedByQuestionPoster: { type: Boolean },
  score: { type: Number, default: Math.random },
  scoreUpdatedAt: { type: Date },
  interestName: { type: String },
  language: {
    type: String,
    enum: languageCodes,
  },
  detectedLanguage: { type: String },
  detectedLanguageConfidence: { type: Number },
  languageMismatch: { type: Boolean },
  awards: {
    type: Map,
    of: Number,
  },
  mentionedUsersText: {
    type: [
      {
        _id: { type: String, ref: 'User' },
        firstName: { type: String },
      }
    ],
    default: undefined,
  },
  mentionedUsersNotified: [{ type: String, ref: 'User' }],

  //app_339
  postedAnonymously: {type:Boolean},
  numAnonymousComments: { type: Number, default: 0 }
});

commentSchema.index({
  flagged: 1,
});

commentSchema.index({
  keywords: 1,
});

commentSchema.index({
  parent: 1,
  createdAt: -1,
});
commentSchema.index({
  parent: 1,
  score: -1,
});
commentSchema.index({
  createdBy: 1,
  createdAt: -1,
});
commentSchema.index({
  createdBy: 1,
  score: -1,
});

// Define methods
// =============================================================================
async function removeComments(comment) {
  console.log('post remove for comment: ', comment._id, comment);

  if (hideSingleCommentAndChildrenAfterBanDelete !== null) {
    await hideSingleCommentAndChildrenAfterBanDelete(null, comment);
  }

  await Notification.deleteMany({ post: comment._id });

  if (comment.image) {
    await s3.deletePicture(comment.image);
  }

  if (comment.audio) {
    await s3.deletePicture(comment.audio);
  }

  for await (const doc of this.model('Comment').find({ parent: comment._id })) {
    console.log('post remove for comment: ', comment._id, ' Removing child comment: ', doc._id, doc.text);
    await doc.deleteOne();
  }
  for await (const doc of this.model('Comment').find({
    parent: comment.parent, postRepliedTo: comment._id,
  })) {
    console.log('post remove for comment: ', comment._id, ' Removing reply: ', doc._id, doc.text);
    await doc.deleteOne();
  }
}

commentSchema.statics.setHideSingleCommentAndChildrenAfterBanDelete = function (fn) {
  hideSingleCommentAndChildrenAfterBanDelete = fn;
};

commentSchema.post('deleteOne', { document: true, query: false }, removeComments);

commentSchema.statics.removeAllForUser = async function (userId) {
  const docs = await this.find({ createdBy: userId });
  for (const doc of docs) {
    await doc.deleteOne();
  }
};

commentSchema.statics.removeAllForProfile = async function (profileId) {
  const docs = await this.find({ profile: profileId });
  for (const doc of docs) {
    await doc.deleteOne();
  }
};

commentSchema.statics.updateSearchFields = async function (commentId) {
  let {
    text
  } = await this.findOne({ _id: commentId }, { text: 1 });
  const keywords = Array.from(new Set([
    ...extractValidWords(text || ''),
  ]));
  const combinedKeywords = Array.from(new Set([
    ...contiguous_keywordsV2(text || '', 5),
  ]));
  const filteredKeywords = keywords.filter(key => !excludedUniverseLinkingKeyWords.has(key.toLowerCase()));
  const linkedExploreKeywords = Array.from(new Set([
    ...findLinkedExploreKeywords(text || ''),
  ]));
  const linkedPillarKeywords = findLinkedPillarKeywords(text);

  const [ linkedKeywords, linkedCategories, linkedSubcategories, linkedProfiles] = await Promise.all([
    Interest.distinct('name', { name: { $in: filteredKeywords }, status: null, numQuestions: { $gte: 5 }}),
    getLinkedCategories(combinedKeywords),
    getLinkedSubcategories(combinedKeywords),
    getLinkedProfiles(combinedKeywords)
  ]);
  await this.updateOne(
    { _id: commentId },
    {
      $set: {
        keywords,
        linkedKeywords,
        linkedExploreKeywords,
        linkedPillarKeywords,
        linkedCategories,
        linkedSubcategories,
        linkedProfiles,
      },
    },
  );
};

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('Comment', commentSchema);
