const mongoose = require('mongoose');
const Notification = require('./notification');
const Comment = require('./comment');
const SavedQuestion = require('./saved-question');
const Interest = require('./interest');
const { languageCodes } = require('../lib/languages');
const s3 = require('../lib/s3');
const { extractValidWords, contiguous_text, contiguous_keywordsV2 } = require('../lib/text-search');

const genderPreferenceLib = require('../lib/gender-preference');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');
const { getLinkedCategories, getLinkedSubcategories, getLinkedProfiles } = require('../lib/database');
const { findLinkedPillarKeywords } = require('../lib/pillar-keywords');
const { findLinkedExploreKeywords } = require('../lib/explore-keywords');
const { excludedUniverseLinkingKeyWords } =require('../lib/exclude-from-universe-programmatic-linking')

const defaultEngagementArray = new Array(24).fill({numComments: 0, numLikes: 0, numClicks: 0, numSecondsReadingOnFeed: 0, numSecondsOnPost: 0, numSecondsReadingComments: 0, numShares: 0});

const questionSchema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  createdBy: { type: String, ref: 'User', default: null },
  title: {
    type: String,
    trim: true,
    maxlength: 300,
  },
  text: {
    type: String,
    trim: true,
    maxlength: 40000,
  },
  keywords: [{ type: String }],
  linkedKeywords: [{ type: String }],
  linkedExploreKeywords: [{ type: String }],
  linkedPillarKeywords: [{
    _id: false,
    keyword: { type: String },
    url: { type: String }
  }],
  linkedCategories: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
  }],
  linkedSubcategories: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
    categoryId: { type: Number },
  }],
  linkedProfiles: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
  }],
  mediaUploadPending: { type: Boolean },
  imageHasText: { type: Boolean },
  image: { type: String },
  images: [{
    image: { type: String }, // URL of the image (from S3)
    altText: { type: String }, // Optional description for each image
    _id: false
  }],
  isVideo: { type: Boolean },
  convertedVideo: { type: String },
  altText: { type: String },
  aspectRatio: { type: Number },
  audio: { type: String },
  audioWaveform: { type: [{ type: Number }], default: undefined },
  audioDuration: { type: Number },
  audioTranscription: { type: String, trim: true, default: undefined },
  gif: { type: String },
  poll: {
    type: {
      _id: false,
      options: [
        {
          _id: false,
          text: { type: String },
          numVotes: { type: Number, default: 0 },
        },
      ],
      votes: {
        type: Map,
        of: Number,
      },
      numVotes: { type: Number, default: 0 },
    },
    default: undefined,
  },
  parent: { type: mongoose.Schema.Types.ObjectId },
  interestName: { type: String },
  hashtags: [ { type: String } ],
  usersThatCommented: [{ type: String, ref: 'User' }],
  anonymousUsersThatCommented: [{ type: String }],
  numUsersThatCommented: { type: Number, default: 0 },
  numComments: { type: Number, default: 0 },
  numHiddenComments: { type: Number, default: 0 },
  numDeletedComments: { type: Number, default: 0 },
  usersWithBannedComments: [{ type: String, ref: 'User' }],
  usersThatLiked: [{ type: String, ref: 'User' }],
  usersThatViewed: [{ type: String, ref: 'User' }],
  usersThatLikedNotified: [{ type: String, ref: 'User' }],
  numLikes: { type: Number, default: 0 },
  numViews: { type: Number, default: 0 },
  numClicks: { type: Number, default: 0 },
  numSecondsReadingOnFeed: { type: Number, default: 0 },
  numSecondsOnPost: { type: Number, default: 0 },
  numSecondsReadingComments: { type: Number, default: 0 },
  numShares: { type: Number, default: 0 },
  hourlyEngagement: {
    type: [
      {
        _id: false,
        numComments: { type: Number, default: 0 },
        numLikes: { type: Number, default: 0 },
        numClicks: { type: Number, default: 0 },
        numSecondsReadingOnFeed: { type: Number, default: 0 },
        numSecondsOnPost: { type: Number, default: 0 },
        numSecondsReadingComments: { type: Number, default: 0 },
        numShares: { type: Number, default: 0 },
      }
    ],
    default: defaultEngagementArray,
  },
  usersThatReported: [{ type: String, ref: 'User' }],
  isDeleted: { type: Boolean, default: false },
  isEdited: { type: Boolean, default: false },
  isBoosted: { type: Boolean, default: false },
  isLightlyBoosted: { type: Boolean },
  flagged: { type: Boolean },
  flaggedByHive: { type: Boolean },
  flaggedByOpenai: { type: Boolean },
  hiveClass: { type: String },
  hiveScore: { type: Number },
  firstLikeAwarded: { type: Boolean },
  banned: { type: Boolean, default: false },
  bannedBy: { type: String, ref: 'User' },
  bannedDate: { type: Date },
  bannedReason: { type: String },
  bannedReasons: { type: [{ type: String }], default: undefined },
  bannedNotes: { type: String, default: undefined },
  noobPost: { type: Boolean },
  score: { type: Number, default: Math.random },
  scoreNoImageMultiplier: { type: Number, default: Math.random },
  scoreWeek: { type: Number, default: Math.random },
  scoreMonth: { type: Number, default: Math.random },
  scoreYear: { type: Number, default: Math.random },
  nonDecayedScore: { type: Number, default: Math.random },
  scoreUpdatedAt: { type: Date },
  region: { type: String },
  webId: { type: String },
  language: {
    type: String,
    enum: languageCodes,
    default: 'en',
  },
  detectedLanguage: { type: String },
  detectedLanguageConfidence: { type: Number },
  languageMismatch: { type: Boolean },
  awards: {
    type: Map,
    of: Number,
  },
  mentionedUsersTitle: {
    type: [
      {
        _id: { type: String, ref: 'User' },
        firstName: { type: String },
      }
    ],
    default: undefined,
  },
  mentionedUsersText: {
    type: [
      {
        _id: { type: String, ref: 'User' },
        firstName: { type: String },
      }
    ],
    default: undefined,
  },
  mentionedUsersNotified: [{ type: String, ref: 'User' }],
  userAttributes: {
    type: {
      _id: false,
      age: { type: Number },
      minAge: { type: Number },
      maxAge: { type: Number },
      genderPreferenceHash: { type: String },
      status: { type: String },
      mbti: { type: String },
      countryCode: { type: String },
      city: { type: String },
      state: { type: String },
      enneagram: { type: String },
      horoscope: { type: String },
      longitude2: { type: Number },
      latitude2: { type: Number },
    },
    default: undefined,
  },
  faceDetected:{type:Boolean},

  //app_339
  postedAnonymously: {type:Boolean},
  numAnonymousComments: { type: Number},

  lastCommentAddedTime: { type: Date },
}, {
  versionKey: false,
});

questionSchema.index({
  keywords: 1,
});

questionSchema.index({
  flagged: 1,
});

questionSchema.index({
  interestName: 1,
  createdAt: -1,
});
questionSchema.index({
  language: 1,
  interestName: 1,
  createdAt: -1,
});
questionSchema.index({
  createdAt: -1,
  interestName: 1,
  language: 1,
});
questionSchema.index({
  language: 1,
  hashtags: 1,
  createdAt: -1,
});
questionSchema.index({
  createdAt: -1,
  interestName: 1,
  'userAttributes.latitude2': 1,
  'userAttributes.longitude2': 1,
  'userAttributes.age': 1,
  'userAttributes.minAge': 1,
  'userAttributes.maxAge': 1,
  'userAttributes.genderPreferenceHash': 1,
  'userAttributes.status': 1,
  'userAttributes.mbti': 1,
  'userAttributes.countryCode': 1,
  'userAttributes.enneagram': 1,
  'userAttributes.horoscope': 1,
});

questionSchema.index({
  interestName: 1,
  score: -1,
});
questionSchema.index({
  language: 1,
  interestName: 1,
  score: -1,
});
questionSchema.index({
  score: -1,
  interestName: 1,
  language: 1,
});
questionSchema.index({
  language: 1,
  hashtags: 1,
  score: -1,
});
questionSchema.index({
  score: -1,
  interestName: 1,
  'userAttributes.latitude2': 1,
  'userAttributes.longitude2': 1,
  'userAttributes.age': 1,
  'userAttributes.minAge': 1,
  'userAttributes.maxAge': 1,
  'userAttributes.genderPreferenceHash': 1,
  'userAttributes.status': 1,
  'userAttributes.mbti': 1,
  'userAttributes.countryCode': 1,
  'userAttributes.enneagram': 1,
  'userAttributes.horoscope': 1,
});
questionSchema.index({
  language: 1,
  region: 1,
  score: -1,
});
questionSchema.index({
  language: 1,
  'userAttributes.countryCode': 1,
  score: -1,
});
questionSchema.index({
  language: 1,
  'userAttributes.city': 1,
  score: -1,
});

questionSchema.index({
  language: 1,
  scoreNoImageMultiplier: -1,
});

questionSchema.index({
  createdBy: 1,
  createdAt: -1,
});
questionSchema.index({
  language: 1,
  createdBy: 1,
  createdAt: -1,
});
questionSchema.index({
  createdAt: -1,
  createdBy: 1,
  language: 1,
});

questionSchema.index({
  createdBy: 1,
  score: -1,
});
questionSchema.index({
  language: 1,
  createdBy: 1,
  score: -1,
});
questionSchema.index({
  score: -1,
  createdBy: 1,
  language: 1,
});

questionSchema.index({
  usersThatCommented: 1,
  createdAt: -1,
});
questionSchema.index({
  usersThatCommented: 1,
  score: -1,
});

questionSchema.index({
  interestName: 1,
  webId: 1,
}, {
  unique: true,
});

questionSchema.index({
  scoreWeek: -1,
});
questionSchema.index({
  interestName: 1,
  scoreWeek: -1,
});
questionSchema.index({
  language: 1,
  scoreWeek: -1,
});

questionSchema.index({
  scoreMonth: -1,
});
questionSchema.index({
  interestName: 1,
  scoreMonth: -1,
});
questionSchema.index({
  language: 1,
  scoreMonth: -1,
});

questionSchema.index({
  scoreYear: -1,
});
questionSchema.index({
  interestName: 1,
  scoreYear: -1,
});
questionSchema.index({
  language: 1,
  scoreYear: -1,
});

questionSchema.index({
  nonDecayedScore: -1,
});
questionSchema.index({
  interestName: 1,
  nonDecayedScore: -1,
});
questionSchema.index({
  language: 1,
  nonDecayedScore: -1,
});

questionSchema.index({
  language: 1,
  'userAttributes.age': 1,
  score: -1,
});
questionSchema.index({
  language: 1,
  'userAttributes.age': 1,
  createdAt: -1,
});

questionSchema.index({
  isVideo: 1,
  language: 1,
  'userAttributes.age': 1,
  score: -1,
}, { partialFilterExpression: { isVideo: true } });
questionSchema.index({
  isVideo: 1,
  language: 1,
  'userAttributes.age': 1,
  createdAt: -1,
}, { partialFilterExpression: { isVideo: true } });
questionSchema.index(
  { createdAt: -1 },
  {
    partialFilterExpression: {
      banned: false,
      numComments: { $gt: 4 }
    },
    name: "createdAt_partial_sitemap_index"
  }
);

// Define methods
// =============================================================================
async function postRemove(question) {
  console.log('post remove for question: ', question._id, question);

  await Notification.deleteMany({ post: question._id });

  await SavedQuestion.removeAllForQuestion(question._id);

  if (question.image) {
    await s3.deletePicture(question.image);
  }

  if (question.images && Array.isArray(question.images)) {
    question.images.map(async (image) => {
      await s3.deletePicture(image.image);
    })
  }

  if (question.audio) {
    await s3.deletePicture(question.audio);
  }

  for await (const doc of Comment.find({ parent: question._id })) {
    console.log('post remove for question: ', question._id, ', Removing child comment: ', doc._id, doc.text);
    await doc.deleteOne();
  }
}
questionSchema.post('deleteOne', { document: true, query: false }, postRemove);

questionSchema.statics.removeAllForUser = async function (userId) {
  await this.updateMany({ createdBy: userId, interestName: 'questions' }, { $set: { createdBy: null } });
  const docs = await this.find({ createdBy: userId });
  for (const doc of docs) {
    await doc.deleteOne();
  }
};

questionSchema.statics.incrementViews = function (questionIds, userId) {
  this.updateMany(
    {
      _id: { $in: questionIds },
      createdBy: { $ne: userId },
      usersThatViewed: { $ne: userId },
    },
    {
      $inc: { numViews: 1 },
      $addToSet: { usersThatViewed: userId },
    },
  ).catch(
    (error) => {
      console.log(`Error:${userId}Adding view count to${questionIds}${error}`);
    },
  );
};

questionSchema.statics.updateSearchFields = async function (questionId) {
  let {
    title, text, poll,
  } = await this.findOne({ _id: questionId }, { title: 1, text: 1, poll: 1 });
  if (poll && poll.options) {
    poll = poll.options.map((x) => x.text).join(' ');
  }
  const keywords = Array.from(new Set([
    ...extractValidWords(title || ''),
    ...extractValidWords(text || ''),
    ...extractValidWords(poll || ''),
  ]));
  const combinedKeywords = Array.from(new Set([
    ...contiguous_keywordsV2(title || '', 5),
    ...contiguous_keywordsV2(text || '', 5),
    ...contiguous_keywordsV2(poll || '', 5),
  ]));
  const filteredKeywords = keywords.filter(key => !excludedUniverseLinkingKeyWords.has(key.toLowerCase()));
  const linkedPillarKeywords = findLinkedPillarKeywords(((title || '') + ' ' + (text || '') + ' ' + (poll || '')).trim());
  const [ linkedKeywords, linkedCategories, linkedSubcategories, linkedProfiles] = await Promise.all([
    Interest.distinct('name', { name: { $in: filteredKeywords }, status: null, numQuestions: { $gte: 5 }}),
    getLinkedCategories(combinedKeywords),
    getLinkedSubcategories(combinedKeywords),
    getLinkedProfiles(combinedKeywords)
  ]);
  const linkedExploreKeywords = Array.from(new Set([
    ...findLinkedExploreKeywords(title || ''),
    ...findLinkedExploreKeywords(text || ''),
  ]));
  await this.updateOne(
    { _id: questionId },
    {
      $set: {
        keywords,
        linkedKeywords,
        linkedPillarKeywords,
        linkedCategories,
        linkedSubcategories,
        linkedProfiles,
        linkedExploreKeywords,
      },
    },
  );
}

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('Question', questionSchema);
