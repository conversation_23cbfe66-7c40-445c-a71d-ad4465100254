/* eslint-disable no-unused-vars */
// const dotenv = require('dotenv').config();
require('log-timestamp');
const morgan = require('morgan');
const morganBody = require('morgan-body');
const bodyParser = require('body-parser');

// disable escaping for i18n - XSS attacks not possible in Flutter and Nextjs
const mustache = require('mustache');
mustache.escape = function(value) { return value; };

const app = require('express')();
const http = require('http');

const mongoose = require('mongoose');
const home = require('./routes/home');
const { applicationError } = require('../lib/http-errors');

const port = process.env.PORT || 3000;
const geocoder = require('../lib/geocoder');
const interestLib = require('../lib/interest');
const sns = require('../lib/sns');
const metricsLib = require('../lib/metrics');
const socketLib = require('../lib/socket');
const cloudwatchLogsLib = require('../lib/cloudwatch-logs');

// Express configuration
// =============================================================================
console.log('INIT ***********************');
console.log('Starting worker, Env: ', process.env.NODE_ENV);

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: false }));

app.use(morgan(
  'combined',
  {
    immediate: true,
  },
));

morganBody(
  app,
  {
    noColors: process.env.NO_COLORS,
  },
);

const server = http.createServer(app);
const io = socketLib.initSocketIo(server);

// Database configuration
// =============================================================================
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

mongoose.connection.on('connecting', () => {
  console.log('dbevent: connecting, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('connected', () => {
  console.log('dbevent: connected, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('disconnecting', () => {
  console.log('dbevent: disconnecting, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('disconnected', () => {
  console.log('dbevent: disconnected, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('reconnected', () => {
  console.log('dbevent: reconnected, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('error', (err) => {
  console.log(`dbevent: error: ${err}, connection state: `, mongoose.connection.readyState);
});
mongoose.connection.on('fullsetup', () => {
  console.log('dbevent: fullsetup, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('all', () => {
  console.log('dbevent: all, connection state: ', mongoose.connection.readyState);
});
mongoose.connection.on('reconnectFailed', () => {
  console.log('dbevent: reconnectFailed, connection state: ', mongoose.connection.readyState);
});

mongoose.connection.once('open', async () => {
  console.log('dbevent: open, connection state: ', mongoose.connection.readyState);

  console.log('Loading interests from database');
  await interestLib.loadInterestsFromDatabase();
  console.log('Finished loading interests from database');

  await metricsLib.loadMostRecentMetrics();

  console.log('Initializing geocoder');
  geocoder.init({
    load: {
      admin1: true, admin2: false, admin3And4: false, alternateNames: false,
    },
  }, () => {
    console.log('Finished initializing geocoder');

    server.listen(port, () => {
      console.log('Express started. Listening on %s', port);
    });
  });
});

mongoose.connect(
  MONGODB_URI,
  {
    autoIndex: true,
    compressors: ['snappy'],
  },
).catch((err) => {
  console.log(`dbevent: initial connection error. Shutting down server. ${err}`);
  process.exit(-1);
});

// Routes
// =============================================================================

app.use('/', home());

// Error Handling
// =============================================================================

// error handler
app.use(async (err, req, res, next) => {
  console.log(err);
  const defaultError = applicationError();
  const status = err.status || defaultError.status;
  const message = err.message;
  if (status >= 500 && status < 600) {
    const errorLog = `[ERROR] User ${req.uid} ${req.method} ${req.url} ${JSON.stringify(req.body)} Response: ${status}`;
    console.log(errorLog, err.stack.replace(/\n/g, ''));
    await sns.publishAlarm(`${errorLog} ${err.stack}`);
    cloudwatchLogsLib.writeCWL(`/boo/${process.env.NODE_ENV}/worker/errors`, JSON.stringify({
      level: "error",
      ts: new Date().toISOString(),
      method: req.method,
      url: req.url,
      body: req.body,
      status: status,
      name: err.name,
      message: err.message,
      stack: err.stack,
    })).catch(() => {/* swallow or console.warn */});;
  }
  next(err);
});

// Default - return 200
app.use((req, res, next) => {
  res.json({});
});

// =============================================================================

module.exports = app;
