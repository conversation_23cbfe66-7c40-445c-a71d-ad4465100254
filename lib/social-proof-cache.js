const { DateTime } = require('luxon');
const BoostMetric = require('../models/boost-metric');
const SocialQueryCache = require('../models/social-query-cache')
const PurchaseReceipt = require('../models/purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt')
const Users = require('../models/user')
const setSocialProofFunctions = {}

async function setSocialProofGeneral(cacheType, cachedData){
  await SocialQueryCache.updateOne(
    { cacheType: cacheType },
    {
      $set: {
        updatedAt: Date.now(),
        cachedData: cachedData,
      }
    },
    { upsert: true }
  )
}

async function getCachedImagesSocialProof(cacheType, setSocialIfExpired = false) {
  const socialProofCacheData = await SocialQueryCache.findOne({ cacheType });
  
  if (!(socialProofCacheData?.cachedData?.length > 0) || (setSocialIfExpired && socialProofCacheData?.updatedAt && (Date.now() - new Date(socialProofCacheData.updatedAt)) > 1 * 60 * 60 * 1000)) {
    const cachedData = await setSocialProofFunctions[cacheType]() || []
    return cachedData;
  }

  return socialProofCacheData.cachedData;
}


setSocialProofFunctions.cachedImages = async () => {
  let cachedImages = []
  const receipts = await PurchaseReceipt
    .find()
    .sort('-purchaseDate')
    .limit(20)
    .populate('user', 'pictures');

  cachedImages = receipts
    .filter((x) => x.user && x.user.pictures.length > 0)
    .map((x) => x.user.pictures[0]);

  cachedImages = [...new Set(cachedImages)];
  await setSocialProofGeneral('cachedImages', cachedImages)
  return cachedImages
}

setSocialProofFunctions.verificationMaleCachedImages = async () => {
  let verificationMaleCachedImages = [];

  const verifiedMale = await Users
    .find({
      'verification.status': 'verified',
      gender: 'male'
    })
    .select('pictures')
    .sort('-createdAt')
    .limit(20)
    .lean();

  verificationMaleCachedImages = verifiedMale
    .filter((x) => x.pictures.length > 0)
    .map((x) => x.pictures[0]);

  verificationMaleCachedImages = [...new Set(verificationMaleCachedImages)];

  await setSocialProofGeneral('verificationMaleCachedImages', verificationMaleCachedImages);
  return verificationMaleCachedImages
}

setSocialProofFunctions.verificationFemaleCachedImages = async () => {
  let verificationFemaleCachedImages = [];

  const verifiedFemale = await Users
    .find({
      'verification.status': 'verified',
      gender: 'female'
    })
    .select('pictures')
    .sort('-createdAt')
    .limit(20)
    .lean();

  verificationFemaleCachedImages = verifiedFemale
    .filter((x) => x.pictures.length > 0)
    .map((x) => x.pictures[0]);

  verificationFemaleCachedImages = [...new Set(verificationFemaleCachedImages)];

  await setSocialProofGeneral('verificationFemaleCachedImages', verificationFemaleCachedImages);
  return verificationFemaleCachedImages
}

setSocialProofFunctions.verificationNonBinaryCachedImages = async () => {
  let verificationNonBinaryCachedImages = [];

  const verifiedNonBinary = await Users
    .find({
      'verification.status': 'verified',
      gender: 'non-binary'
    })
    .select('pictures')
    .sort('-createdAt')
    .limit(20)
    .lean();

  verificationNonBinaryCachedImages = verifiedNonBinary
    .filter((x) => x.pictures.length > 0)
    .map((x) => x.pictures[0]);

  verificationNonBinaryCachedImages = [...new Set(verificationNonBinaryCachedImages)];

  await setSocialProofGeneral('verificationNonBinaryCachedImages', verificationNonBinaryCachedImages);
  return verificationNonBinaryCachedImages
}

setSocialProofFunctions.superLikeCachedImages = async () => {
  let superLikeCachedImages = [];

  const receipts = await SuperLikePurchaseReceipt
    .find()
    .sort('-purchaseDate')
    .limit(20)
    .populate('user', 'pictures');

  superLikeCachedImages = receipts
    .filter((x) => x.user && x.user.pictures.length > 0)
    .map((x) => x.user.pictures[0]);

  // remove duplicates
  superLikeCachedImages = [...new Set(superLikeCachedImages)];

  await setSocialProofGeneral('superLikeCachedImages', superLikeCachedImages);
  return superLikeCachedImages
}

setSocialProofFunctions.coinPurchaseCachedImages = async () => {
  let coinPurchaseCachedImages = [];

  const receipts = await CoinPurchaseReceipt
    .find()
    .sort('-purchaseDate')
    .limit(20)
    .populate('user', 'pictures');

  coinPurchaseCachedImages = receipts
    .filter((x) => x.user && x.user.pictures.length > 0)
    .map((x) => x.user.pictures[0]);

  // remove duplicates
  coinPurchaseCachedImages = [...new Set(coinPurchaseCachedImages)];

  await setSocialProofGeneral('coinPurchaseCachedImages', coinPurchaseCachedImages);
  return coinPurchaseCachedImages
}

setSocialProofFunctions.neuronPurchaseCachedImages = async () => {
  let neuronPurchaseCachedImages = [];

  const receipts = await NeuronPurchaseReceipt
    .find()
    .sort('-purchaseDate')
    .limit(20)
    .populate('user', 'pictures');

    neuronPurchaseCachedImages = receipts
    .filter((x) => x.user && x.user.pictures.length > 0)
    .map((x) => x.user.pictures[0]);

  // remove duplicates
  neuronPurchaseCachedImages = [...new Set(neuronPurchaseCachedImages)];

  await setSocialProofGeneral('neuronPurchaseCachedImages', neuronPurchaseCachedImages);
  return neuronPurchaseCachedImages
}

setSocialProofFunctions.boostPurchaseCachedImages = async () => {
  let boostPurchaseCachedImages = [];

  const receipts = await BoostPurchaseReceipt
    .find()
    .sort('-purchaseDate')
    .limit(20)
    .populate('user', 'pictures');

  boostPurchaseCachedImages = receipts
    .filter((x) => x.user && x.user.pictures.length > 0)
    .map((x) => x.user.pictures[0]);

  // remove duplicates
  boostPurchaseCachedImages = [...new Set(boostPurchaseCachedImages)];

  await setSocialProofGeneral('boostPurchaseCachedImages', boostPurchaseCachedImages);
  return boostPurchaseCachedImages
}

setSocialProofFunctions.boostSuccessCachedData = async () => {
  let boostSuccessCachedData = [];
  const twentyFourHoursAgo = DateTime.utc().minus({ hours: 24 }).toJSDate();
  const successfulBoosts = await BoostMetric
    .find({
      createdAt: { $gte: twentyFourHoursAgo },
      numLikesReceived: { $gte: 5 }
    })
    .sort('-createdAt')
    .limit(20)
    .populate('user', 'pictures')
    .select('numLikesReceived user');

  // remove duplicates  
  const addedImage = new Set();
  boostSuccessCachedData = successfulBoosts
    .reduce((acc, boost) => {
      if (boost.user && boost.user.pictures.length > 0) {
        const image = boost.user.pictures[0];
        if (!addedImage.has(image)) {
          addedImage.add(image);
          acc.push({
            image,
            numLikes: boost.numLikesReceived
          });
        }
      }
      return acc;
    }, []);

  await setSocialProofGeneral('boostSuccessCachedData', boostSuccessCachedData);
  return boostSuccessCachedData;
}

async function setCacheImagesSocialProof(req, res, next) {
  if (!process.env.TESTING) {
    // always return 200 immediately
    res.json({});
  }
  await setSocialProofFunctions.cachedImages()
  await setSocialProofFunctions.verificationMaleCachedImages()
  await setSocialProofFunctions.verificationFemaleCachedImages()
  await setSocialProofFunctions.verificationNonBinaryCachedImages()
  await setSocialProofFunctions.superLikeCachedImages()
  await setSocialProofFunctions.coinPurchaseCachedImages()
  await setSocialProofFunctions.neuronPurchaseCachedImages()
  await setSocialProofFunctions.boostPurchaseCachedImages()
  await setSocialProofFunctions.boostSuccessCachedData()
  if (process.env.TESTING) {
    // for test environments, return at end
    res.json({});
  }
}

module.exports = {
  getCachedImagesSocialProof,
  setCacheImagesSocialProof,
}