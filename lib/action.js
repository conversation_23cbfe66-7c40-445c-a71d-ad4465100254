const mongoose = require('mongoose');
const lookup = require('country-code-lookup');
const Action = require('../models/action');
const Block = require('../models/block');
const User = require('../models/user');
const Chat = require('../models/chat');
const HideList = require('../models/hide-list');
const Follow = require('../models/follow');
const FriendList = require('../models/friend-list');
const UserMetadata = require('../models/user-metadata');
const ExclusionList = require('../models/exclusion-list');
const UsersWhoLiked = require('../models/users-who-liked');
const UnBlocked = require('../models/unblocked');
const { getAddress } = require('./location');
const { getHoroscope, getAge } = require('./horoscope');
const { checkForDeleteInstantMatch } = require('./chat');
const { calculateViewableInDailyProfiles } = require('./basic');
const { sendSocketEvent } = require('../lib/socket');
const ExclusionListRecalculation = require('../models/exclusion-list-recalculation');

async function migrateAllBlockActions() {
  try {
    const start = new Date().getTime();
    const actions = mongoose.connection.db.collection('actions');
    const blocks = mongoose.connection.db.collection('blocks');
    let totalMigrated = 0;
    const cursor = actions.find({ block: true }, { projection: { createdAt: 1, from: 1, to: 1, hide: 1 } });
    for await (const action of cursor) {
      const updateFields = { createdAt: action.createdAt };
      if (action.hide) {
        updateFields.hide = true;
      }
      await blocks.updateOne(
        { from: action.from, to: action.to },
        { $setOnInsert: updateFields },
        { upsert: true },
      );
      ++totalMigrated;
      if (totalMigrated % 100 == 0) {
        console.log(`Num blocks migrated: ${totalMigrated}`);
      }
    }
    await cursor.close();
    const end = new Date().getTime();
    console.log(`Time taken for total ${totalMigrated} block action migration : ${end - start} ms`);
  } catch (error) {
    console.log('Error migrating block action:', error.message);
  }
}

async function addToExclusionList(userId, otherUserId) {
  await User.updateOne(
    { _id: userId },
    { $pull: { recentRecommendations: otherUserId, 'currentDayMetrics.cachedUsersWhoLiked': otherUserId } },
  );
  await User.updateOne(
    { _id: userId, 'metrics.currentExclusionListSize': { $gte : 0 } },
    { $inc: { 'metrics.currentExclusionListSize': 1 }}
  );
  await UsersWhoLiked.removeUser(userId, otherUserId);
  try {
    const rv = await ExclusionList.updateOne(
      {
        user: userId,
        exclusionList: { $ne: otherUserId },
      },
      {
        $push: { exclusionList: otherUserId },
      },
    );
    if (rv.matchedCount == 0) {
      await ExclusionList.updateOne(
        { user: userId },
        { $addToSet: { exclusionList: otherUserId } },
        { upsert: true },
      );
    }
  } catch (err) {
    if (err?.message?.includes('Resulting document after update is larger than')) {
      const exclusionData = await ExclusionList.aggregate([
        { $match: { user: userId } }, 
        { $project: { size: { $size: "$exclusionList" } } }, 
      ]);
      const exclusionListSize = exclusionData.length > 0 ? exclusionData[0].size : 0;
      await User.updateOne(
        { _id: userId },
        { exclusionListFailed: true, sizeOfExclusionUsedDuringFailure: exclusionListSize },
      );
      await recalculateExclusionList(userId, true);
    } else {
      throw err;
    }
  }
}

async function addToTopProfilesExclusionList(userId, otherUserId) {
  try {
    const rv = await ExclusionList.updateOne(
      {
        user: userId,
        topProfilesExclusionList: { $ne: otherUserId },
      },
      {
        $push: { topProfilesExclusionList: otherUserId },
      },
    );
    if (rv.matchedCount == 0) {
      await ExclusionList.updateOne(
        { user: userId },
        { $addToSet: { topProfilesExclusionList: otherUserId } },
        { upsert: true },
      );
    }
  } catch (err) {
    if (err?.message?.includes('Resulting document after update is larger than')) {
      const exclusionData = await ExclusionList.aggregate([
        { $match: { user: userId } }, 
        { $project: { size: { $size: "$exclusionList" } } }, 
      ]);
      const exclusionListSize = exclusionData.length > 0 ? exclusionData[0].size : 0;
      await User.updateOne(
        { _id: userId },
        { exclusionListFailed: true, sizeOfExclusionUsedDuringFailure: exclusionListSize },
      );
      await recalculateExclusionList(userId, true);
    } else {
      throw err;
    }
  }
}

async function bucketUpdatedAtAllUsers() {
  const users = mongoose.connection.db.collection('users');
  const query = {};
  const options = { projection: { _id: 1, updatedAt: 1 } };
  const cursor = users.find(query, options);
  for await (const user of cursor) {
    console.log(user);

    const filter = { _id: user._id };
    const updateDoc = {
      $set: {
        updatedAt: new Date(new Date(user.updatedAt).setUTCHours(0, 0, 0, 0)),
      },
    };
    const result = await users.updateOne(filter, updateDoc, options);
  }
}

async function migrateLocationAllUsers() {
  const users = mongoose.connection.db.collection('users');
  const query = {};
  const options = {
    projection: {
      _id: 1,
      location: 1,
      actualLocation: 1,
      teleportLocation: 1,
    },
  };
  const cursor = users.find(query, options);
  let i = 0;
  for await (const user of cursor) {
    console.log(i, user);

    const filter = { _id: user._id };
    const updates = {};
    if (user.location && user.location.coordinates) {
      const address = getAddress(user.location, user.timezone);
      updates.countryCode = address.countryCode;
      updates.country = address.country;
      updates.state = address.state;
      updates.city = address.city;
    }
    if (user.actualLocation && user.actualLocation.coordinates) {
      const address = getAddress(user.actualLocation, user.timezone);
      updates.actualCountryCode = address.countryCode;
      updates.actualCountry = address.country;
      updates.actualState = address.state;
      updates.actualCity = address.city;
    }
    if (user.teleportLocation && user.teleportLocation.coordinates) {
      const address = getAddress(user.teleportLocation, user.timezone);
      updates.teleportCountryCode = address.countryCode;
      updates.teleportCountry = address.country;
      updates.teleportState = address.state;
      updates.teleportCity = address.city;
    }

    console.log(i, updates);
    const result = await users.updateOne(filter, { $set: updates }, options);
    i++;
  }
}

async function calculateHoroscopeAllUsers() {
  const users = mongoose.connection.db.collection('users');
  const query = { horoscope: null, birthday: { $ne: null } };
  const options = {
    projection: {
      _id: 1,
      birthday: 1,
    },
  };
  const cursor = users.find(query, options);
  let i = 0;
  let bulk = User.collection.initializeUnorderedBulkOp();
  for await (const user of cursor) {
    if (!user.birthday) {
      continue;
    }
    const filter = { _id: user._id };
    const updates = { $set: { horoscope: getHoroscope(user.birthday) } };
    bulk.find(filter).update(updates);
    i++;
    if (i % 100 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = User.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function calculateAgeAllUsers() {
  const res = await User.updateMany(
    {
      birthday: { $ne: null },
    },
    [
      {
        $set: {
          age: {
            $subtract: [
              { $subtract: [{ $year: "$$NOW" }, { $year: "$birthday" }] },
              { $cond: [{ $lt: [{ $dayOfYear: "$birthday" }, { $dayOfYear: "$$NOW" }] }, 0, 1] }
            ]
          }
        }
      },
    ],
  );
  console.log('calculateAgeAllUsers', res);
}

async function calculateViewableAllUsers() {
  const users = mongoose.connection.db.collection('users');
  const query = { viewableInDailyProfiles: null };
  const options = {
    projection: {
      _id: 1,
      hidden: 1,
      banned: 1,
      shadowBanned: 1,
      hideLocation: 1,
      pictures: 1,
      location: 1,
      viewableInDailyProfiles: 1,
    },
  };
  const cursor = users.find(query, options);
  let i = 0;
  let bulk = User.collection.initializeUnorderedBulkOp();
  for await (const user of cursor) {
    const filter = { _id: user._id };
    const updates = { $set: { viewableInDailyProfiles: calculateViewableInDailyProfiles(user) } };
    bulk.find(filter).update(updates);
    i++;
    if (i % 100 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = User.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function migrateCountryCodeAllUsers() {
  const users = mongoose.connection.db.collection('users');
  const query = {};
  const options = {
    projection: {
      _id: 1,
      country: 1,
      actualCountry: 1,
      teleportCountry: 1,
    },
  };
  const cursor = users.find(query, options);
  for await (const user of cursor) {
    const filter = { _id: user._id };
    const updates = {};
    if (user.country) {
      updates.countryCode = lookup.byCountry(user.country).iso2;
    }
    if (user.actualCountry) {
      updates.actualCountryCode = lookup.byCountry(user.actualCountry).iso2;
    }
    if (user.teleportCountry) {
      updates.teleportCountryCode = lookup.byCountry(user.teleportCountry).iso2;
    }

    console.log(user, updates);
    const result = await users.updateOne(filter, { $set: updates }, options);
  }
}

async function removePassesFromUser(user) {
  const result = await Action.deleteMany({ from: user._id, pass: true, like: false, block: false, hide: false  });
  console.log(`Removed passes from user ${user._id}: ${JSON.stringify(result)}`);

  await recalculateExclusionList(user._id, false, true);
}

async function recalculateExclusionList(userId, saveExclusionListRecalculation, fromRevival) {
  if(!fromRevival){
    let userData = await User.findOne({ _id: userId }).select({ startTimeOfExclusionListRecalculation: 1 });
    if (!userData || (userData.startTimeOfExclusionListRecalculation && userData.startTimeOfExclusionListRecalculation > (new Date(Date.now() - 30 * 60 * 1000)))) {
      return;
    }
  }
  await User.updateOne({ _id: userId },{ $set: { startTimeOfExclusionListRecalculation: new Date()  } });
  let actionQuery = { from: userId }
  /* 
  if (user && (user.metrics.numActionsSent > 400000 || user.exclusionListFailed)) {
    const threeMonthsAgo = new Date(Date.now() - 3 * 30 * 24 * 60 * 60 * 1000) // 3 months before
    actionQuery.createdAt = { $gte: threeMonthsAgo }
  }
  */
  let exclusionList = []
  {
    const start = new Date().getTime();
    const latestActions = await Action.find(actionQuery)
    .sort({ createdAt: -1 }) 
    .limit(300000)         
    .select("to"); 
    const sentActionIds = [...new Set(latestActions.map(action => action.to))];
    const receivedBlockIds = await Block.distinct("from", { to: userId });
    const sentBlockIds = await Block.distinct("to", { from: userId });
    const matchIds = await FriendList.distinct("friends", { userId: userId });
    exclusionList = sentActionIds.concat(receivedBlockIds).concat(matchIds).concat(sentBlockIds)
    const end = new Date().getTime();
    console.log(`User ${userId} Time to load actions: ${end-start} ms.`);
  }

  exclusionList = [...new Set(exclusionList)]; // remove duplicates
  if (saveExclusionListRecalculation) {
    const exclusionListLength = await ExclusionList.aggregate([
      { $match: { user: userId } }, 
      {
        $project: {
          _id: 0, 
          exclusionListLength: { $size: "$exclusionList" }, 
        },
      },
    ]);
    const exclusionListLengthBefore = exclusionListLength[0]?.exclusionListLength || 0;
    await ExclusionListRecalculation.create({
      user: userId,
      exclusionListLengthBefore: exclusionListLengthBefore,
      exclusionListLengthAfter: exclusionList.length,
    })
  }
  await ExclusionList.updateOne(
    { user: userId },
    { $set: { exclusionList } },
    { upsert: true },
  );
  let userUpdateData =  { 
    exclusionListFailed: false, 
    'metrics.currentExclusionListSize': exclusionList.length 
  }
  if (saveExclusionListRecalculation) {
    userUpdateData.lastExclusionRecalculated = new Date()
  }
  await User.updateOne(
    { _id: userId },
    { $set: userUpdateData, $unset: { startTimeOfExclusionListRecalculation: 1 } },
  );
}

async function addCreatedAtToActions() {
  await mongoose.connection.db.collection('actions').updateMany(
    {},
    [{ $set: { createdAt: { $toDate: '$_id' } } }],
  );
}

async function blockUser(userId, otherUserId, io, hideUser, autoBlockedReason, autoBlockedWith, blockedDeviceId) {
  console.log(`user: ${userId} blocked: ${otherUserId}${autoBlockedReason ? ` for reason: ${autoBlockedReason}` : ''}`);

  // During deviceId change to blocked device/creating account with blocked device
  // we send autoBlockedReason and blockedDeviceId only
  if (autoBlockedReason === 'DeviceId' && blockedDeviceId) {
    const blocks = await Block.find({
      from: userId,
      $or: [{ to: otherUserId }, { blockedDeviceId }],
    }).lean();

    for (const block of blocks) {
      if (block.to === otherUserId) return;
      if (block.blockedDeviceId === blockedDeviceId) autoBlockedWith = block.to;
    }
    blockedDeviceId = undefined; // For auto blocks, don't store the device id in the block
    if (!autoBlockedWith) {
      // Cannot tag with a user for whom this device is blocked
      // so marking the block as a manual block. User can unblock if needed.
      autoBlockedReason = undefined;
    }
  } else if (await Block.isBlocked(userId, otherUserId)) {
    return;
  }

  await Block.create({
    from: userId,
    to: otherUserId,
    autoBlockedReason,
    autoBlockedWith,
    blockedDeviceId,
    hide: hideUser || undefined,
  });

  /// /TODO: Update only modified ones
  // add to exclusion list
  await addToExclusionList(userId, otherUserId);
  await addToExclusionList(otherUserId, userId);

  // update metrics
  await Promise.all([
    User.incrementMetrics(userId, ['numBlocksSent']),
    User.incrementMetrics(otherUserId, ['numBlocksReceived'])
  ]);

  // delete the chat if it exists
  const chat = await Chat.findDirectChat(userId, otherUserId);
  if (chat) {
    await Chat.removeChatAndMessages(chat);
    console.log(`Removed chat ${chat}`);

    sendSocketEvent(otherUserId, 'deleted chat', { _id: chat._id });
    await checkForDeleteInstantMatch(chat);

    // remove from friend list
    await FriendList.removeFriendship(userId, otherUserId);
  }

  // remove follows
  await Follow.removeFollow(userId, otherUserId);
  await Follow.removeFollow(otherUserId, userId);

  // remove from unblocked collection if exists
  await UnBlocked.deleteOne({ from: userId, to: otherUserId });
}

async function blockUsersWithDeviceId(userId, blockingUser, io) {
  const idsToBlock = (await User.find(
    {
      _id: { $nin: [userId, blockingUser._id] },
      deviceId: blockingUser.deviceId,
    },
    { _id: 1 },
  )).map((doc) => doc._id);

  console.log(`user: ${userId} blocked users: ${idsToBlock}`);
  await HideList.hideUserFromDeviceId(userId, blockingUser.deviceId);
  await blockUser(userId, blockingUser._id, io, false, undefined, undefined, blockingUser.deviceId);
  await Promise.all(idsToBlock.map((otherUserId) => blockUser(userId, otherUserId, io, false, 'DeviceId', blockingUser._id)));
}

async function removeFromExclusionList(userId, otherUserId) {
  await ExclusionList.updateOne(
    { user: userId },
    { $pull: { exclusionList: otherUserId } },
  );
}

async function removeFromTopProfilesExclusionList(userId, otherUserId) {
  await ExclusionList.updateOne(
    { user: userId },
    { $pull: { topProfilesExclusionList: otherUserId } },
  );
}

async function postMutualUnBlock(user1, user2) {
  await Promise.all([
    removeFromExclusionList(user1, user2),
    removeFromExclusionList(user2, user1),
    removeFromTopProfilesExclusionList(user1, user2),
    removeFromTopProfilesExclusionList(user2, user1),
  ]);
}

async function unBlockUser(fromUser, toUser, manualUnblock = false) {
  // Remove block
  const deleteBlock = await Block.deleteOne({ from: fromUser, to: toUser });
  if (deleteBlock.deletedCount === 1) {
    // update metrics
    await User.updateOne({ _id: fromUser, 'metrics.numBlocksSent': { $gt: 0 } }, { $inc: { 'metrics.numBlocksSent': -1 } });
    await User.updateOne({ _id: toUser, 'metrics.numBlocksReceived': { $gt: 0 } }, { $inc: { 'metrics.numBlocksReceived': -1 } });

    // Check if the other user has blocked this user
    if (!await Block.isBlocked(toUser, fromUser)) {
      await postMutualUnBlock(fromUser, toUser);
    }

    // Add to recently unblocked list
    if (manualUnblock) {
      await UnBlocked.create({ from: fromUser, to: toUser });
    }

    return true;
  }
  return false;
}

/**
 * This function removes auto-blocks placed on users due to a shared device ID.
 * Users are auto-blocked when they have the same `deviceId` as another user who was blocked.
 * During this auto-blocking process, responsible user's ID is stored as `autoBlockedWith`.
 *
 * When the originally blocked user is unblocked, this function removes all related auto-blocks.
 * Additionally, when a user is blocked, their `deviceId` is added to the blocker's `HideList`.
 * This function ensures that the corresponding `deviceId` is removed from the `HideList` upon unblocking.
 *
 * @param {String} userId - The ID of the user performing the unblock.
 * @param {String} autoBlockedWith - The ID of the user whose block triggered the auto-blocks.
 */
async function unBlockAutoBlockedForDeviceId(userId, autoBlockedWith) {
  const autoBlockedUsers = await Block.find(
    { from: userId, autoBlockedReason: 'DeviceId', autoBlockedWith },
    { to: 1, _id: 1 },
  );
  if (!autoBlockedUsers.length) return;

  const autoBlockedUserIds = autoBlockedUsers.map(user => user.to);
  const deviceIds = (await User.find({ _id: { $in: autoBlockedUserIds } }, { deviceId: 1 }))
    .map(user => user.deviceId).filter(Boolean);
  await HideList.updateOne({ userId }, { $pull: { deviceIds: { $in: deviceIds } } });

  const blockedUsers = (await Block.find({ from: { $in: autoBlockedUserIds }, to: userId }, { to: 1 }))
    .map(block => block.to);

  const unBlockPromises = autoBlockedUsers
    .filter(user => !blockedUsers.includes(user.to))
    .map(user => postMutualUnBlock(userId, user.to));
  await Promise.all(unBlockPromises);

  await Block.deleteMany({ _id: { $in: autoBlockedUsers.map(x => x._id) } });
}

async function unHideUsers(userId) {
  await User.updateOne({ _id: userId }, { $set: { hiddenContacts: false } });
  await HideList.updateOne({ userId }, { emailsList: [], phonesList: [] });
  const hiddenUsers = await Block.find({ from: userId, hide: true }, { from: 1, to: 1 });
  for (const hiddenUser of hiddenUsers) {
    await unBlockUser(userId, hiddenUser.to);
  }
}

async function hideUsers(userId, io, emails, numbers) {
  await User.updateOne({ _id: userId }, { $set: { hiddenContacts: true } });

  const friendIds = await FriendList.getFriendIds(userId);
  friendIds.push(userId);

  const friends = await User.find(
    { _id: { $in: friendIds } },
    { email: 1, phoneNumber: 1 },
  );

  const friendEmails = new Set(friends.map(friend => friend.email).filter(Boolean));
  const friendNumbers = new Set(friends.map(friend => friend.phoneNumber).filter(Boolean));

  const filteredEmails = emails.filter(email => !friendEmails.has(email));
  const filteredNumbers = numbers.filter(number => !friendNumbers.has(number));

  if (filteredEmails.length || filteredNumbers.length) {
    await HideList.hideUserFromEmailNumbers(userId, filteredEmails, filteredNumbers);
    const toHideUsers = await User.find(
      {
        _id: { $nin: friendIds },
        $or: [
          { email: { $in: filteredEmails } },
          { phoneNumber: { $in: filteredNumbers } },
        ],
      },
      { _id: 1 },
    );

    await Promise.all(toHideUsers.map(({ _id }) => blockUser(userId, _id, io, true)));
  }
}

/**
 * This function enforces auto-blocking when a previously blocked user changes their `deviceId`.
 * If a user who was already blocked switches to a new device, all users associated with the new `deviceId`
 * should also be blocked.
 *
 * The function follows these steps:
 * 1. Retrieves all users who share the new `deviceId`.
 * 2. Identifies the list of users who have already blocked this user.
 * 3. For each blocking user:
 *    - Adds the new `deviceId` to their `HideList`.
 *    - Auto-blocks all users with the same `deviceId`, avoiding self-blocking.
 *
 * @param {Object} user - The user who was already blocked but changed their `deviceId`.
 * @param {Object} io - res.io
 */

async function blockSameDeviceUsers(user, io) {
  const sameDeviceUsers = (await User.find(
    { _id: { $ne: user._id }, deviceId: user.deviceId },
    { _id: 1 },
  )).map(doc => doc._id.toString());
  if (!sameDeviceUsers.length) return;

  const blockingUsers = await Block.find(
    { to: user._id },
    { from: 1, autoBlockedWith: 1 },
  ).lean();
  if (!blockingUsers.length) return;

  const blockPromises = blockingUsers
    .map(({ from, autoBlockedWith }) => {
      HideList.hideUserFromDeviceId(from, user.deviceId);

      return sameDeviceUsers
        .filter(deviceUserId => deviceUserId !== from)
        .map(deviceUserId =>
          blockUser(from, deviceUserId, io, false, 'DeviceId', autoBlockedWith || user._id),
        );
    }).flat();

  Promise.all(blockPromises).catch(err => console.log(`For User ${user._id}: Error blocking same device users`, err));
}

module.exports = {
  addToExclusionList,
  addToTopProfilesExclusionList,
  removePassesFromUser,
  addCreatedAtToActions,
  bucketUpdatedAtAllUsers,
  migrateCountryCodeAllUsers,
  migrateLocationAllUsers,
  calculateHoroscopeAllUsers,
  calculateViewableAllUsers,
  calculateAgeAllUsers,
  blockUser,
  blockUsersWithDeviceId,
  hideUsers,
  unHideUsers,
  migrateAllBlockActions,
  unBlockUser,
  recalculateExclusionList,
  unBlockAutoBlockedForDeviceId,
  blockSameDeviceUsers,
};
