require('log-timestamp');
const morgan = require('morgan');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const http = require('http');
const asyncHandler = require('express-async-handler');
const multer = require('multer');

// disable escaping for i18n - XSS attacks not possible in Flutter and Nextjs
const mustache = require('mustache');
mustache.escape = function(value) { return value; };

const port = process.env.PORT || 3000;
const { HttpError, applicationError } = require('./http-errors');
const { i18n } = require('./translate');
const admin = require('../config/firebase-admin');
const userMiddleware = require('../middleware/user');
const cloudwatchLogsLib = require('../lib/cloudwatch-logs');
const geocoder = require('./geocoder');
const socketLib = require('./socket');
const sns = require('./sns');
const { verifyAdmin } = require('../middleware/user');

const health = require('../routes/health');
const googleRenewal = require('../routes/google-renewal');
const admob = require('../routes/admob');
const webRoutes = require('../routes/web');
const user = require('../routes/user');
const chat = require('../routes/chat');
const message = require('../routes/message');
const report = require('../routes/report');
const personality = require('../routes/personality');
const appRating = require('../routes/app-rating');
const coins = require('../routes/coins');
const referral = require('../routes/referral');
const feedback = require('../routes/feedback');
const teleportRoutes = require('../routes/teleport');
const comment = require('../routes/comment');
const question = require('../routes/question');
const adminRoutes = require('../routes/admin');
const notificationRoutes = require('../routes/notification');
const followRoutes = require('../routes/follow');
const configRoutes = require('../routes/config');
const socialProofRoutes = require('../routes/social-proof');
const interestRoutes = require('../routes/interest');
const stickerPackRoutes = require('../routes/sticker-pack');
const { globalRateLimitEachRouteMiddleware, totalRateLimiterForAllRouteMiddleware } = require('../middleware/user-rate-limiter')
const { API_RATE_LIMIT } = require('../lib/constants')

function excludeFromAlarm(url, err) {
  const excludeErrors = [
    'Error: Timeout',
    'Invalid JSON response',
  ];
  const isExcludedError = excludeErrors.some(excludeError => err?.includes(excludeError));
  const isAiUrl = url?.includes('/v1/ai/');
  return isAiUrl && isExcludedError;
}

function createServer(app) {
  const server = http.createServer(app);

  // https://shuheikagawa.com/blog/2019/04/25/keep-alive-timeout/
  server.keepAliveTimeout = 181 * 1000;

  // https://github.com/nodejs/node/issues/27363
  server.headersTimeout = 182 * 1000;

  const io = socketLib.initSocketIo(server);
  app.use((req, res, next) => {
    res.io = io;
    next();
  });

  // Express configuration
  // =========================================================================
  console.log('INIT ***********************');
  console.log('Starting web server, Env: ', process.env.NODE_ENV);

  app.use(i18n.init);

  function allExceptStripeWebhook(fn) {
    return function(req, res, next) {
      if (req.path === '/stripe-webhook' && req.method === 'POST') {
        next();
      } else {
        fn(req, res, next);
      }
    }
  }

  app.use(allExceptStripeWebhook(bodyParser.json({ limit: '50mb' })));
  app.use(allExceptStripeWebhook(bodyParser.urlencoded({ limit: '50mb', extended: false })));

  // trust proxy in order to log remote IP address
  app.enable("trust proxy");

  app.use(morgan(
    (tokens, req, res) => [
      `[${tokens.date(req, res, 'iso')}]`,
      'User',
      req.uid || '-',
      tokens.method(req, res),
      tokens.url(req, res),
      'Response:',
      tokens.status(req, res),
      tokens['response-time'](req, res), 'ms',
      '-', tokens.res(req, res, 'content-length'), 'bytes',
      'AppVersion:',
      req.user && req.user.appVersion ? req.user.appVersion : '-',
      'IpAddress:',
      tokens['remote-addr'](req, res),
    ].join(' '),
    {
      skip: (req, res) => req.originalUrl.startsWith('/health')
            || req.method == 'OPTIONS',
    },
  ));

  return server;
}

function configureRoutes(app) {
  // Routes
  // =========================================================================
  app.use(cors({ origin: '*' }));

  // these routes do not require authentication
  app.use('/health', health());
  app.use('/google-renewal', googleRenewal());
  app.use('/ios-renewal', require('../routes/ios-renewal')());
  app.use('/admob', admob());
  app.use('/stripe-webhook', require('../routes/stripe-webhook')());
  app.use('/aiimagewebhook', require('../routes/ai-image-webhook')());
  app.use('/tenjin', require('../routes/tenjin')());
  app.use('/web', webRoutes());
  app.use('/pusher', require('../routes/pusher')());
  app.use('/zendesk', require('../routes/zendesk')());

  // these routes require authentication without finding user
  app.use('/event', [admin.verifyToken]);
  app.use('/event', require('../routes/event')());

  // these routes require authentication with finding user
  app.use(admin.verifyToken);
  app.use(asyncHandler(userMiddleware.findUser));
  app.use(admin.verifyAppCheck);
  /*
  app.use(
  totalRateLimiterForAllRouteMiddleware(API_RATE_LIMIT.TOTAL_ROUTE_LIMIT, API_RATE_LIMIT.TOTAL_ROUTE_LIMIT_BLOCK_SESSION_TIME_FOR_LIMIT, false, 1, API_RATE_LIMIT.TOTAL_ROUTE_LIMIT_BLOCK_TIME_AFTER_LIMIT),
  globalRateLimitEachRouteMiddleware(API_RATE_LIMIT.GLOBAL_PER_ROUTE_LIMIT, API_RATE_LIMIT.GLOBAL_PER_ROUTE_BLOCK_SESSION_TIME_FOR_LIMIT, false, 1, API_RATE_LIMIT.GLOBAL_PER_ROUTE_BLOCK_TIME_AFTER_LIMIT),
)
  */
  app.use(userMiddleware.verifyNotBanned);
  app.use(asyncHandler(userMiddleware.checkIp));
  app.use(asyncHandler(userMiddleware.updateLastSeen));
  app.use('/v1/user', user());
  app.use('/v1/chat', chat());
  app.use('/v1/message', message());
  app.use('/v1/report', report());
  app.use('/v1/personality', personality());
  app.use('/v1/app-rating', appRating());
  app.use('/v1/coins', coins());
  app.use('/v1/referral', referral());
  app.use('/v1/feedback', feedback());
  app.use('/v1/teleport', teleportRoutes());
  app.use('/v1/comment', comment());
  app.use('/v1/question', question());
  app.use('/v1/notification', notificationRoutes());
  app.use('/v1/follow', followRoutes());
  app.use('/v1/config', configRoutes());
  app.use('/v1/social-proof', socialProofRoutes());
  app.use('/v1/interest', interestRoutes());
  app.use('/v1/sticker-pack', stickerPackRoutes());
  app.use('/v1/database', require('../routes/database')());
  app.use('/v1/stripe', require('../routes/stripe')());
  app.use('/v1/liveness', require('../routes/liveness')());
  app.use('/v1/liveness-new', require('../routes/azure-liveness')());
  app.use('/v1/question-view-data', require('../routes/question-view-data')());
  app.use('/v1/story', require('../routes/story')());
  app.use('/v1/super-like', require('../routes/super-like')());
  app.use('/v1/profile-view', require('../routes/profile-view')());
  app.use('/v1/ai', require('../routes/ai')());
  app.use('/v1/boosts', require('../routes/boosts')());
  app.use('/blogs', require('../routes/blogs')());
  app.use("/v1/ai/image", require("../routes/ai-image")());

  // admin routes
  app.use('/v1/admin', [verifyAdmin]);
  app.use('/v1/admin', adminRoutes());

  // CORS
  // =========================================================================
  // app.use(cors({origin: '*'}));

  // Error Handling
  // =========================================================================

  // multer errors
  app.use((err, req, res, next) => {
    if (err instanceof multer.MulterError) {
      console.log(err);

      // If a response has already gone out, don’t try to mutate it.
      if (res.headersSent) return next(err);

      return res.status(422).send(req.__('There\'s an issue with this photo or video, please select another one.'));
    }
    /*
    if (err.name == 'ValidationError') {
      // If a response has already gone out, don’t try to mutate it.
      if (res.headersSent) return next(err);

      return res.status(422).send('Invalid Input');
    }
    */
    next(err);
  });

  // error handler
  app.use(async (err, req, res, next) => {
    const defaultError = applicationError();
    const status = err.status || defaultError.status;
    let message = err.message || req.__(defaultError.message);
    if (status != 401) {
      console.log(err);
    }
    if (status >= 500 && status < 600) {
      const errorLog = `[ERROR] User ${req.uid} ${req.method} ${req.url} ${JSON.stringify(req.body)} Response: ${status}`;
      console.log(errorLog, err.stack.replace(/\n/g, ''));
      if (!excludeFromAlarm(req.url, err.stack)) {
        await sns.publishAlarm(`${errorLog} ${err.stack}`);
        cloudwatchLogsLib.writeCWL(`/boo/${process.env.NODE_ENV}/api/errors`, JSON.stringify({
          level: "error",
          ts: new Date().toISOString(),
          user: req.uid,
          method: req.method,
          url: req.url,
          body: req.body,
          status: status,
          name: err.name,
          message: err.message,
          stack: err.stack,
        })).catch(() => {/* swallow or console.warn */});;
      }
      message = req.__(defaultError.message);
    }

    // If a response has already gone out, don’t try to mutate it.
    if (res.headersSent) return next(err);

    if (process.env.NODE_ENV != 'dev') {
      // For non-dev environments, don't send the stacktrace
      res.status(status).send(message);
    } else {
      // Development error handler sends stacktraces to client
      next(err);
    }
  });

  // Default 404
  app.use((req, res, next) => {
    const err = new HttpError(404, 'Not Found');
    next(err);
  });
}

module.exports = {
  createServer,
  configureRoutes,
};
