const aws = require('aws-sdk');
const os = require('os');

const logs = new aws.CloudWatchLogs({
  accessKeyId: process.env.AWS_KEY,
  secretAccessKey: process.env.AWS_SECRET,
  region: 'us-east-1',
});

function getInstanceId() {
  return new Promise((resolve) => {
    const md = new aws.MetadataService({ httpOptions: { timeout: 1000 } });
    md.request('/latest/meta-data/instance-id', (err, id) => resolve(err ? null : id));
  });
}

let STREAM;
async function getStreamName() {
  if (!STREAM) STREAM = (await getInstanceId()) || os.hostname() || 'main';
  return STREAM;
}

async function writeCWL(logGroupName, message) {
  const logStreamName = await getStreamName();
  const params = {
    logGroupName,
    logStreamName,
    logEvents: [{ timestamp: Date.now(), message: String(message) }],
  };

  try {
    await logs.putLogEvents(params).promise();
  } catch (err) {
    if (err.code === 'ResourceNotFoundException') {
      // create-if-missing, then retry once
      await logs.createLogGroup({ logGroupName }).promise().catch(() => {});
      await logs.createLogStream({ logGroupName, logStreamName }).promise().catch(() => {});
      await logs.putLogEvents(params).promise();
      return;
    }
    throw err;
  }
}

module.exports = {
  writeCWL,
};
