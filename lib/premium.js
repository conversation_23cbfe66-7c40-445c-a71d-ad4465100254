const cmp = require('semver-compare');
const { DateTime } = require('luxon');
const moment = require('moment');
const User = require('../models/user');
const constants = require('./constants');
const pricingConfigLib = require('./pricing-config');

function buildProductIds(user, flashSaleOverride = undefined) {
  // product ids
  const boo_infinity_1_week = 'boo_infinity_1_week';
  const boo_infinity_1_month = 'boo_infinity_1_month';
  const boo_infinity_3_months = 'boo_infinity_3_months';
  const boo_infinity_6_months = 'boo_infinity_6_months';
  const boo_infinity_12_months = 'boo_infinity_12_months';
  const boo_infinity_lifetime = 'boo_infinity_lifetime';

  let discount = '';
  let premiumFlashSale = getPremiumFlashSale(user);
  const infinity_sale_active = !!premiumFlashSale;

  // override flash sale for generating product ids with sale
  if (!premiumFlashSale && flashSaleOverride) {
    premiumFlashSale = flashSaleOverride;
  }

  if (premiumFlashSale) {
    discount = `_discount_${premiumFlashSale.discount}`;
  }

  let variant = '';
  if (moment().diff(user.birthday, 'years') <= 23) {
    variant = '_variant_1';
  } else {
    if (user.preferences.dating.length > 0 && user.preferences.friends.length == 0) {
      variant = '_variant';
    }
    if (user.preferences.dating.length == 0 && user.preferences.friends.length > 0) {
      variant = '_variant_1';
    }
  }

  let product_ids = [
    boo_infinity_1_month + variant,
    boo_infinity_3_months + discount + variant,
    boo_infinity_6_months + discount + variant,
  ];

  if (user.versionAtLeast('1.11.31')) {
    product_ids = [
      boo_infinity_1_month + variant,
      boo_infinity_3_months + discount + variant,
      boo_infinity_6_months + discount + variant,
      boo_infinity_12_months + discount + variant,
    ];
  }

  if (user.versionAtLeast('1.11.33')) {
    product_ids = [
      boo_infinity_12_months + discount + variant,
      boo_infinity_6_months + discount + variant,
      boo_infinity_3_months + discount + variant,
      boo_infinity_1_month + variant,
    ];
  }

  if (user.versionAtLeast('1.11.36')) {
    product_ids = [
      boo_infinity_6_months + discount + variant,
      boo_infinity_3_months + discount + variant,
      boo_infinity_1_month + variant,
      boo_infinity_lifetime,
    ];
  }

  if (user.versionAtLeast('1.11.48')) {
    product_ids = [
      boo_infinity_12_months + discount + variant,
      boo_infinity_6_months + discount + variant,
      boo_infinity_3_months + discount + variant,
      boo_infinity_1_month + variant,
      boo_infinity_lifetime,
    ];
  }

  // pricing experiments
  let version;
  let variantNewFormat = '';
  let variantNewFormatWithX3 = '';

  if (moment().diff(user.birthday, 'years') <= 23) {
    variantNewFormat = '_x2';
    variantNewFormatWithX3 = '_x3';
  } else {
    if (user.preferences.dating.length > 0 && user.preferences.friends.length == 0) {
      variantNewFormat = '_x1';
      variantNewFormatWithX3 = '_x1';
    }
    if (user.preferences.dating.length == 0 && user.preferences.friends.length > 0) {
      variantNewFormat = '_x2';
      variantNewFormatWithX3 = '_x2';
    }
  }

  let variantToUse = variantNewFormat;
  const config = pricingConfigLib.getPricingConfig(user);

  if (config) {
    if (config.baseline > 1) {
      version = `_v${config.baseline}`;
    }

    if (config.variant > 1) {
      let key;
      if (user.os == 'android') {
        key = `infinity_price_v${config.variant}`;
      }
      if (user.os == 'ios') {
        key = `ios_infinity_price_v${config.variant}`;
      }
      if (key && user.config[key] == true) {
        version = `_v${config.variant}`;
      }
    }

    if (version) {
      if (premiumFlashSale) {
        discount = `_d${premiumFlashSale.discount}`;
      }

      if (user.os == 'ios' && version == '_v3') {
        // on ios v3, college has a new variant x3
        variantToUse = variantNewFormatWithX3;
      }

      product_ids = [
        `infinity_m12${version}${variantToUse}${discount}`,
        `infinity_m6${version}${variantToUse}${discount}`,
        `infinity_m3${version}${variantToUse}${discount}`,
        `infinity_m1${version}${variantToUse}`,
        `infinity_lifetime${version}`,
      ];

      if (user.os == 'ios' && version == '_v2') {
        // handle typo in product id
        product_ids[4] = 'infinity_lifetime_V2';
      }

      if (user.os == 'ios' && version == '_v3' && variantToUse == '_x2') {
        // ios v3_x2 products begin with "Infinity_"
        product_ids = [
          `Infinity_m12${version}${variantToUse}${discount}`,
          `Infinity_m6${version}${variantToUse}${discount}`,
          `Infinity_m3${version}${variantToUse}${discount}`,
          `Infinity_m1${version}${variantToUse}`,
          `infinity_lifetime${version}`,
        ];
      }
    }
  }

  if (flashSaleOverride) {
    product_ids = product_ids.filter(id => id.includes('_discount_') || id.includes('_d'));
    return { product_ids };
  }

  return { product_ids, version, variantToUse, infinity_sale_active };
}

function getPremiumFlashSale(user) {
  if (
    isPremium(user)
    || user.metrics.numPurchases > 0
    || !user.premiumFlashSaleEndDate
    || user.premiumFlashSaleEndDate < new Date()
  ) {
    return undefined;
  }

  const rv = {
    discount: 50,
    saleEndDate: user.premiumFlashSaleEndDate,
  }

  if (user.premiumFlashSaleOneTime) {
    rv.oneTime = true;
  }

  return rv;
}

async function getSuperLikeFlashSale(user) {
  if (user.metrics.numSuperLikePurchases > 0) {
    return undefined;
  }

  if (!user.superLikeFlashSaleEndDate && moment().diff(user.createdAt, 'days') >= 8) {
    user.superLikeFlashSaleEndDate = DateTime.utc().plus({ hours: 6 }).toJSDate();
    await user.save();
  }

  let discount = 50;

  if (user.superLikeFlashSaleEndDate > new Date()) {
    return {
      discount,
      saleEndDate: user.superLikeFlashSaleEndDate,
    };
  }

  return undefined;
}

async function getCoinsFlashSale(user) {
  if (user.metrics.numCoinPurchases > 0) {
    return undefined;
  }

  if (!user.coinsFlashSaleEndDate && moment().diff(user.createdAt, 'days') >= 9) {
    user.coinsFlashSaleEndDate = DateTime.utc().plus({ hours: 6 }).toJSDate();
    await user.save();
  }

  let discount = 30;

  if (user.coinsFlashSaleEndDate > new Date()) {
    return {
      discount,
      saleEndDate: user.coinsFlashSaleEndDate,
    };
  }

  return undefined;
}

async function getBoostsFlashSale(user) {
  if (!user.boostsFlashSaleEndDate && moment().diff(user.createdAt, 'days') >= 3) {
    user.boostsFlashSaleEndDate = DateTime.utc().plus({ hours: 6 }).toJSDate();
    await user.save();
  }

  let discount = 50;

  if (user.boostsFlashSaleEndDate > new Date()) {
    return {
      discount,
      saleEndDate: user.boostsFlashSaleEndDate,
    };
  }

  return undefined;
}

function isPremium(user) {
  return isPremiumV1(user) || isPremiumV2(user) || isGodMode(user);
}

function isPremiumV1OrGodMode(user) {
  return isPremiumV1(user) || isGodMode(user);
}

function isPremiumV1(user) {
  if (['Russia'].includes(user.actualCountry)) {
    return true; // temporary measure due to payment issues
  }
  if (user.admin || user.translator) {
    return true;
  }
  if (user.premiumExpiration === undefined) {
    return false;
  }
  return user.premiumExpiration > new Date();
}

function isPremiumV2(user) {
  if (user.premiumV2Expiration === undefined) {
    return false;
  }
  return user.premiumV2Expiration > new Date();
}

function isGodMode(user) {
  if (user.godModeExpiration === undefined) {
    return false;
  }
  return user.godModeExpiration > new Date();
}

function isCrown(user) {
  return user.translator || user.crown;
}

function isInterplanetary(user) {
  return true;
}

module.exports = {

  getPremiumFlashSale,
  getSuperLikeFlashSale,
  getCoinsFlashSale,
  getBoostsFlashSale,
  isPremium,
  isPremiumV1,
  isPremiumV2,
  isGodMode,
  isPremiumV1OrGodMode,
  isCrown,
  buildProductIds,

  isInterplanetary,

  getPreferences(user) {
    const preferences = JSON.parse(JSON.stringify(user.preferences));

    if (preferences.distance) {
      if (!(isPremium(user) || isInterplanetary(user)) && preferences.distance > constants.maxDistanceFilter) {
        preferences.distance = constants.maxDistanceFilter;
      }
      preferences.global = undefined;
    }

    // backwards compatibility
    if (!user.appVersion || cmp(user.appVersion, '1.10.17') < 0) {
      if (!preferences.gender) {
        preferences.gender = [];
      }
      if (!preferences.purpose) {
        preferences.purpose = ['dating', 'friends'];
      }
    }

    // backwards compatibility
    if (preferences.gender && preferences.gender.length > 0) {
      if (!preferences.purpose || preferences.purpose.includes('dating')) {
        preferences.dating = preferences.gender;
      }
      if (!preferences.purpose || preferences.purpose.includes('friends')) {
        preferences.friends = preferences.gender;
      }
    }

    // backwards compatibility
    if (!preferences.dating) {
      preferences.dating = [];
    }
    if (!preferences.friends) {
      preferences.friends = [];
    }

    // if (preferences.datingSubPreferences) {
    //   if (isPremium(user) && preferences.datingSubPreferences && preferences.datingSubPreferences.length) {
    //     preferences.datingSubPreferences = preferences.datingSubPreferences
    //   } else {
    //     preferences.datingSubPreferences = undefined
    //   }
    // }

    // if (preferences.relationshipStatus) {
    //   if (isPremium(user) && preferences.relationshipStatus && preferences.relationshipStatus.length) {
    //     preferences.relationshipStatus = preferences.relationshipStatus
    //   } else {
    //     preferences.relationshipStatus = undefined
    //   }
    // }

    return preferences;
  },

};
