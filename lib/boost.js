const { DateTime } = require("luxon");
const BoostMetric = require("../models/boost-metric");
const BoostTransaction = require('../models/boost-transaction');
const { updateUserScore } = require("../lib/score");
const coinsLib = require("../lib/coins");
const { sendSocketEvent } = require('./socket');

async function activateBoostByPurchase(user, durationMinutes) {
  if (user.isBoostActive()) {

    const totalDurationMinutes = durationMinutes;

    const newExpirationDate = DateTime.fromJSDate(user.boostExpiration)
      .plus({ minutes: totalDurationMinutes })
      .toJSDate();

    user.boostExpiration = newExpirationDate;
    user.boostDurationMinutes += totalDurationMinutes;
    user.postBoostPopupHandled = undefined;
    user.metrics.numActionsReceivedDuringBoostQuota = user.metrics.numActionsReceivedDuringBoostQuota + 100; 
    await user.save();

    const mostRecentBoost = await BoostMetric.findOne({ user: user._id }).sort(
      "-boostExpiration"
    );
    mostRecentBoost.durationMinutes += totalDurationMinutes;
    mostRecentBoost.purchaseBoostUsed += 1;
    mostRecentBoost.numBoosts += 1;
    mostRecentBoost.boostExpiration = newExpirationDate;
    await mostRecentBoost.save();
  } else {

    user.boostExpiration = DateTime.utc()
      .plus({ minutes: durationMinutes })
      .toJSDate();
    user.boostDurationMinutes = durationMinutes;
    user.postBoostPopupHandled = undefined;
    user.metrics.numActionsReceivedDuringBoostQuota = 100; 
    await user.save();

    const boostMetric = new BoostMetric({
      user: user._id,
      durationMinutes,
      boostExpiration: user.boostExpiration,
      purchaseBoostUsed: 1,
      numBoosts: 1,
    });
    await boostMetric.save();
  }

  await updateUserScore(user);

  // deduct user boosts
  user.numBoosts -= 1;

  // APP-903 - trigger boost sale if user has no more purchased boosts and hasn't triggered in last 14 days
  if (
    user.numBoosts === 0 &&
    user.isConfigTrue('app_903') &&
    (!user.boostsSaleTriggerDate || user.boostsSaleTriggerDate <= DateTime.utc().minus({ days: 14 }).toJSDate())
  ) {
    user.boostsFlashSaleEndDate = DateTime.utc().plus({ hours: 6 }).toJSDate();
    user.boostsSaleTriggerDate = DateTime.utc().toJSDate();
    user.metrics.numBoostsSalesTriggered = (user.metrics.numBoostsSalesTriggered ?? 0) + 1;

    // send socket event to prompt boost sale
    sendSocketEvent(user._id, 'triggerBoostsFlashSale', { event: 'opened_boosts_sale_triggered_popup' });
  }

  await user.save();

  await BoostTransaction.create({
    user: user._id,
    transactionAmount: -1,
    newBalance: user.numBoosts,
    description: 'used boost',
  });

  return {boostExpiration :user.boostExpiration}


}

module.exports = { activateBoostByPurchase };
