const { DateTime } = require('luxon');
const moment = require('moment');
const _ = require('underscore');
const Report = require('../models/report');
const PreemptiveModerationLog = require('../models/preemptive-moderation-log');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Follow = require('../models/follow');
const Chat = require('../models/chat');
const Message = require('../models/message');
const BannedSource = require('../models/banned-source');
const BannedUser = require('../models/banned-user');
const BannedFace = require('../models/banned-face');
const { getMessages } = require('./message');
const admin = require('../config/firebase-admin');
const socketLib = require('./socket');
const coinsLib = require('./coins');
const { translate, translate_frontend } = require('./translate');
const { copyToBanned, copyEvidenceToUserFolder, generateHashFromS3 } = require('./s3');
const { addBannedFaceToIndex, findBannedFace, UPDATED_BANNED_USERS_COLL_ID, deleteFacesById } = require('./rekognition');
const { lambda } = require('../lib/lambda');
const { cloudwatch } = require('../lib/cloudwatch');
const openai = require('../lib/openai');
const constants = require('../lib/constants');
const sns = require('../lib/sns');
const InterestPoint = require('../models/interest-point');
const socialLib = require('../lib/social');
const { verifyProfilePicture } = require('../lib/verification');
const { isValidPicture } = require('../lib/basic');
const mongoose = require('mongoose');
const emailLib = require('../lib/email');
const BannedFile = require('../models/banned-file');
const { executeAggregationWithRetry } = require('./retry-aggregate-query');
const chatLib = require('../lib/chat');
const countryLib = require('./country');
const { saveShadowBanUnmatchRecord, deleteShadowBanUnmatchRecord } = require('./chat');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const BanAppeal = require('../models/ban-appeal');
const TempBanAppeal = require('../models/temp-ban-appeal');

const REPORTS_UNTIL_ACCOUNT_SHADOW_BAN = 3;

async function addPicturesToBannedFile(user, bannedImages, otherParams = {}) {
  let results = await Promise.all(bannedImages.map(async (imageKey) => {
    if (imageKey === otherParams.key) return { imageHash: otherParams.imageHash, imageKey };
    const hash = await generateHashFromS3(imageKey);
    return hash ? { imageHash: hash, imageKey } : null;
  }));
  results = results.filter(x => x);

  if (results.length === 0) {
    return;
  }

  const updateData = { $push: {} };
  updateData.$push.bannedImages = { $each: results };

  await BannedFile.findOneAndUpdate(
    { user: user._id },
    updateData,
    { upsert: true },
  );
}

function matchesScammerCondition(user, checkTimeZone = false) {
  if (checkTimeZone && user?.timezone === 'Asia/Manila' && user?.gender === 'female' && user?.verification?.status === 'unverified' && (user?.productIdPurchased?.includes('m1_') || user?.productIdPurchased?.includes('1_month'))) {
    return true;
  }

  if (!checkTimeZone && user?.gender === 'female' && user?.verification?.status === 'unverified' && (user?.productIdPurchased?.includes('m1_') || user?.productIdPurchased?.includes('1_month'))) {
    return true;
  }
  return false;
}

async function awardUserForReport(user) {
  if (!user) {
    return;
  }
  const coins = await coinsLib.updateCoins(
    { user: user._id },
    { $inc: { coins: 50 } },
    'successful report',
  );
  const coinReward = [{
    caption: translate('Successful User Report', user.locale),
    rewardAmount: 50,
    newTotal: coins,
  }];
  socketLib.sendCoinRewards(user._id, coinReward);
  admin.sendNotification(
    user,
    null,
    translate('Thank You', user.locale),
    translate('You’ve been awarded for your successful report and keeping the community safe.', user.locale),
    null,
    null,
    'general',
    'successful-report',
  );
}

const geoBanOfficialReason = 'Boo is currently not yet available in your region.';
const geoBanReasons = [
  "Auto-ban: Nigeria",
  "Auto-ban: Togo",
  "Auto-ban: Ghana",
  "Auto-ban: Senegal",
  "Auto-ban: Vientiane",
  "Nigeria",
  "Togo",
  "Ghana",
  "Senegal",
  "Vientiane",
];

const banReasonMapping = [
  [
    'scamming',
    [
      'scamming',
      'Scammer',
      'Scamming',
      'banned email domain',
      'asking for money in messages',
      'subscribed Asia/Manila spam',
      'bannedByAdmin',
      'scammer verification photo cleanup',
      'banned by cleanup script using email domain',
      'Scammer New Account',
      'timezone + country',
      'Auto-ban: timezone does not match country',
      'singapore',
      'Auto-ban: banned location',
      'whatsapp keyword in message',
      'non-binary AI pose',
      'subscribed spam',
      'Wan Chai',
      'new signup on old version (banned by cleanup script)',
      'hong kong',
      'Guangdong',
      'banned location: SA Rivendell',
      'Scammer Old Account',
      'fraudulent purchase with invalid bundleId',
      'timezone + work',
      'Auto-ban: banned email domain',
    ],
  ],
  [
    'creating multiple accounts',
    [
      'creating multiple accounts',
      'banned face found in liveness verification picture (new logic)',
      'banned file found in profile picture',
      'Auto-ban: deviceId',
      'deviceId',
      'new signup on old version',
      'Auto-ban: loginSource already banned',
      'banned face found in profile picture',
      'banned face found in liveness verification picture',
      'banned face found in verification picture',
      'banned face found in verification picture (new logic)',
      'banned face found in profile picture during report trigger',
      'banned face found in verification picture during report trigger',
      'Auto-ban: same deviceId, different gender',
      'same deviceId, different gender',
    ],
  ],
  [
    'underage',
    [
      'underage',
      'Underage',
      'Auto-ban: underage',
      'underage birthday',
    ],
  ],
  [
    'impersonation',
    [
      'impersonation',
      'Impersonation',
    ],
  ],
  [
    'spam',
    [
      'spam',
      'Spam',
      'infringing text found in name',
      'spam in interests',
      'Auto-ban: education',
      'Auto-ban: spam handle',
      'keyword',
      'spam (banned by cleanup script)',
      'university of porto',
      'bannedByTranslator',
      'timezone + country + work',
      'reports',
      'spam in interests (banned by cleanup script)',
      'Other',
    ],
  ],
  [
    geoBanOfficialReason,
    geoBanReasons,
  ],
  [
    'prostitution',
    [
      'prostitution',
      'Prostitution and Trafficking',
    ],
  ],
  [
    'inappropriate messages',
    [
      'inappropriate messages',
      'Inappropriate Messages',
    ],
  ],
  [
    'inappropriate profile',
    [
      'inappropriate profile',
      'Inappropriate Profile',
      'All pictures shadow hidden',
      'inappropriate profile',
    ],
  ],
  [
    'repeatedly violating terms',
    [
      'repeatedly violating terms',
      'Multiple temp bans',
      '5 temp bans from 5 different reporters within 30 days',
      '10 posts banned within 30 days',
      '10 comments within 10 different post threads banned within 30 days',
    ],
  ],
  [
    'unverified',
    [
      'soft ban for unverified users',
      'unverified with infringing text (preemptive moderation)',
      'unverified with infringing text (sexual content in image found by preemptive moderation)',
      'new signup on old version (soft ban)',
      'unverified with banned email domain',
      'unverified web with unfamiliar email domain',
      'unverified with infringing text',
      'unverified with instagram spam in photos',
      'unverified with infringing text (found by OCR moderation)',
      'unverified with infringing text (saved banned infringing text)',
      'unverified with infringing text (saved banned infringing text backfill)',
      'unverified with banned email domain (backfill)',
      'unverified with instagram spam in photos (banned by cleanup script)',
    ],
  ],
];

const banReasonToRuleMapping = {
  scamming: {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: 'Spam, solicit for money, or defraud other users.',
  },
  'creating multiple accounts': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Use another user's account, share an account with another user, or have multiple accounts. Create a new account without our permissions if we have already terminated your account.`,
  },
  underage: {
    policy: 'Terms of Service',
    section: '2. Eligibility Requirements',
    rule: `You must be at least 18 years of old to be eligible to create an account on Boo and use the Service.`,
  },
  impersonation: {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Impersonate another person or entity, or post photographs of another person without their consent.`,
  },
  spam: {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Spam, solicit for money, or defraud other users.`,
  },
  prostitution: {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Spam, solicit for money, or defraud other users.`,
  },
  'inappropriate messages': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Publish any Content that is hate speech, threatening, sexually explicit, or pornographic; incites violence, or contains nudity, graphic or gratuitous violence.`,
  },
  'inappropriate profile': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Publish any Content that is hate speech, threatening, sexually explicit, or pornographic; incites violence, or contains nudity, graphic or gratuitous violence.`,
  },
  'nudity/sexual content': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Publish any Content that is hate speech, threatening, sexually explicit, or pornographic; incites violence, or contains nudity, graphic or gratuitous violence.`,
  },
  harassment: {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Bully, "stalk", intimidate, assault, harass, mistreat, or defame any individual.`,
  },
  'violence and physical harm': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Publish any Content that is hate speech, threatening, sexually explicit, or pornographic; incites violence, or contains nudity, graphic or gratuitous violence.`,
  },
  'hate speech': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Publish any Content that is hate speech, threatening, sexually explicit, or pornographic; incites violence, or contains nudity, graphic or gratuitous violence.`,
  },
  'spam, promotion or solicitation': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Spam, solicit for money, or defraud other users.`,
  },
  'revealing another person’s private or sensitive information': {
    policy: 'Terms of Service',
    section: '8. Community Rules',
    rule: `Impersonate another person or entity, or post photographs of another person without their consent.`,
  },
};

async function evaluateBanNotice(user) {
  if (user.banNotice?.reason == 'spam' && geoBanReasons.includes(user.bannedReason)) {
    user.banNotice.reason = geoBanOfficialReason;
    await user.save();
    return;
  }

  if (!user || !user.shadowBanned || user.banNotice?.reason) {
    return;
  }

  let reason;

  if (!user.bannedReason) {
    reason = 'spam';
  }

  for (const element of banReasonMapping) {
    const mappedReason = element[0];
    const originalReasons = element[1];
    if (originalReasons.includes(user.bannedReason)) {
      reason = mappedReason;
      break;
    }
  }

  if (user.bannedReasons) {
    const mappedReasons = [];
    for (const reason of user.bannedReasons) {
      for (const element of banReasonMapping) {
        const mappedReason = element[0];
        const originalReasons = element[1];
        if (originalReasons.includes(reason)) {
          if (mappedReason != 'unverified') {
            mappedReasons.push(mappedReason);
          }
          break;
        }
      }
    }
    if (mappedReasons.length) {
      reason = mappedReasons.join(', ');
    }
  }

  // unverified ban reasons exempt from ban notice
  if (!reason || reason == 'unverified') {
    return;
  }

  user.banNotice = {
    reason,
    appealStatus: 'allowed',
  };
  await user.save();
}

async function autoShadowBan(user, bannedReason, comment) {
  const isNewUser = moment().diff(user.createdAt, 'days') < 30;
  const report = await createReport(
    user,
    null,
    [`Auto-report: ${bannedReason}`],
    comment,
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    isNewUser,
  );
  if (!report) {
    return;
  }
  if (isNewUser || bannedReason == 'deviceId') {
    await shadowBan(user, null, `Auto-ban: ${bannedReason}`, comment);
  }
}

async function changeNumberYourTurnChats(reportedUserId, changeValue) {
  const chats = await Chat.find(
    {
      perUserState: {
        $elemMatch: {
          userId: reportedUserId,
        },
      },
      messaged: true,
      deletedAt: null,
      groupChat: { $ne: true },
    }
  )
  .select({ perUserState: 1, users: 1  })
  .lean();

  const userIdsToDecrement = new Set(); // reportedUserId will have only 1 chat with other user so making a set

  for (const chat of chats) {
    if(changeValue == -1){
      for(const userId of chat.users){
        if(userId !== reportedUserId){
          socketLib.sendSocketEvent(userId, 'deleted chat', { _id: chat._id });
        }
      }
    }
    for (const state of chat.perUserState) {
      if (state.userId !== reportedUserId && ['uncategorized', 'yourTurn'].includes(state.yourTurnState)) {
        userIdsToDecrement.add(state.userId);
      }
    }
  }
  if (userIdsToDecrement.size > 0) {
    await User.updateMany(
      { _id: { $in: [...userIdsToDecrement] } },
      { $inc: { 'metrics.numYourTurnChats': changeValue } }
    );
    for(const userId of userIdsToDecrement){
        await chatLib.sendSocketUpdateForNumYourTurnChats(userId)
    }
  }
  if(changeValue == -1){
    await User.updateOne(
      { _id: reportedUserId },
      { $set: { 'metrics.numYourTurnChats': 0 } }
    );
  }else if(changeValue == 1){
    const numYourTurnChats = await chatLib.backfillUserNumYourTurnChats(reportedUserId)
    await User.updateOne(
      { _id: reportedUserId },
      { $set: { 'metrics.numYourTurnChats': numYourTurnChats } }
    );
  }
  await chatLib.sendSocketUpdateForNumYourTurnChats(reportedUserId)
}

async function shadowBan(user, bannedBy, bannedReason, bannedNotes, otherParams = {}, bannedReasons = []) {
  if (!user || user._id == chatLib.BOO_SUPPORT_ID || user._id == chatLib.BOO_BOT_ID) {
    return;
  }
  if (user.cannotBeBanned) {
    await emailLib.sendAlertForBanAttemptOnBanExemptUser(`Attempted shadow ban on user ${user._id} (name: ${user.firstName}, email: ${user.email}), bannedBy: ${bannedBy}, bannedReason: ${bannedReason}, bannedNotes: ${bannedNotes}`);
    return;
  }
  const alreadyBanned = user.shadowBanned;
  const reportedUserId = user._id;
  console.log(`Shadow banning user ${reportedUserId} for reason ${bannedReason}`);

  user.profileTempBanReason = undefined;
  user.profileTempBanReportId = undefined;
  user.shadowBanned = true;
  user.bannedBy = bannedBy;
  user.bannedReason = bannedReason;
  user.bannedNotes = bannedNotes;
  user.bannedDate = Date.now();
  user.banHistory.push({
    action: 'ban',
    by: bannedBy,
    date: Date.now(),
    reason: bannedReason,
    notes: bannedNotes,
  });
  user.calculateViewableInDailyProfiles();
  if (user.verification.status == 'pending') {
    await user.setVerificationStatus('rejected', 'auto rejected due to ban', bannedBy);
  }
  if (Array.isArray(bannedReasons) && bannedReasons.length > 0 && bannedReasons.every(reason => typeof reason === 'string')) {
    user.bannedReasons = bannedReasons;
  }
  await user.save();

  // award coins to users who sent a report
  /* no longer needed due to report automation
  let reports = await Report
    .find({ reportedUser: reportedUserId, status: 'needs review' })
    .populate('reportedBy');
  for (const report of reports) {
    await awardUserForReport(report.reportedBy);
  }
  */

  await Report.verifyReports({ reportedUser: reportedUserId }, bannedBy);
  await Follow.removePendingFollowsFrom(reportedUserId);

  // dismiss reports from the banned user
  reports = await Report
    .find({ reportedBy: reportedUserId, status: 'needs review' });
  for (const report of reports) {
    report.status = 'dismissed';
    report.updatedAt = Date.now();
    await report.save();
    await User.incrementMetric(report.reportedUser, 'numPendingReports', -1);
  }

  // update chats
  await Chat.updateMany(
    {
      users: reportedUserId,
      groupChat: { $ne: true },
    },
    {
      $addToSet: {
        bannedUsers: reportedUserId,
      },
    },
  );

  if(!alreadyBanned){
    await changeNumberYourTurnChats(reportedUserId, -1);
  }

  // add to unmatch list
  await saveShadowBanUnmatchRecord(user);

  // update interest points
  await InterestPoint.updateMany(
    { user: reportedUserId },
    { shadowBanned: true },
  );

  {
    const entries = await InterestPoint.find({
      user: reportedUserId,
      rank: { $gt: 0 },
    });
    for (const entry of entries) {
      await socialLib.updateInterestRanks(entry.interest, entry.language);
    }
  }

  if (alreadyBanned) {
    return;
  }

  const bannedPhotoPaths = [];
  // copy profile images to banned directory
  // New banned face adding logic: add only verification photos to new banned face collection in rekognition
  const faceIdPhotos = [];

  const applyBannedFaceLogic = (bannedReason != 'temp shadow ban due to inappropriate profile');

  if (user.pictures && user.pictures.length > 0) {
    for (let i = 0; i < user.pictures.length; i++) {
      const newPath = await copyToBanned(user.pictures[i], user._id);
      if (newPath) {
        bannedPhotoPaths.push(newPath);
      }
    }
    if (applyBannedFaceLogic) {
      await addPicturesToBannedFile(user, user.pictures, otherParams);
    }
  }
  if (user.isVerified()) {
    if (user.verification.pictures?.length > 0) {
      const verificationPicture = user.verification.pictures[user.verification.pictures.length - 1];
      if (applyBannedFaceLogic) {
        faceIdPhotos.push(verificationPicture);
      }
      const newPath = await copyToBanned(verificationPicture, user._id);
      if (newPath) {
        bannedPhotoPaths.push(newPath);
      }
    }
    if (user.livenessVerification?.frames?.length > 0) {
      const verificationPicture = user.livenessVerification.frames[0].key;
      if (applyBannedFaceLogic) {
        faceIdPhotos.push(verificationPicture);
      }
      const newPath = await copyToBanned(verificationPicture, user._id);
      if (newPath) {
        bannedPhotoPaths.push(newPath);
      }
    }
  }

  const faceIds = (
    await Promise.all(
      faceIdPhotos.map(async (photo) => await addBannedFaceToIndex(photo, user._id, UPDATED_BANNED_USERS_COLL_ID)),
    )
  ).filter((faceId) => faceId != null);

  // add data to bannedUser collection
  await User.aggregate([
    { $match: { _id: user._id } },
    {
      $set: { pictures: bannedPhotoPaths }
    },
    {
      $replaceRoot: {
        newRoot: {
          userData: '$$ROOT',
          user: user._id,
        }
      }
    },
    {
      $set: { faceIds },
    },
    {
      $lookup: {
        from: 'messages',
        localField: 'user',
        foreignField: 'sender',
        as: 'messages',
      }
    },
    {
      $lookup: {
        from: 'questions',
        localField: 'user',
        foreignField: 'createdBy',
        as: 'questions',
      }
    },
    {
      $lookup: {
        from: 'comments',
        localField: 'user',
        foreignField: 'createdBy',
        as: 'comments',
      }
    },
    {
      $merge: {
        into: 'bannedusers',
      }
    }
  ]);

  if (['Scamming', 'Spam, Promotion or Solicitation', 'Underage', 'Prostitution and Trafficking', 'Scammer', 'Spam'].includes(bannedReason)) {
    const matchesFromColorado = await Chat.aggregate([
      {
        $match: {
          users: user._id,
          pendingUser: null,
          deletedAt: null,
        }
      },
      {
        $sort: {
          lastMessageTime: -1,
        }
      },
      {
        $unwind: '$users',
      },
      {
        $match: {
          users: { $ne: user._id },
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'users',
          foreignField: '_id',
          as: 'otherUser',
        }
      },
      {
        $unwind: '$otherUser'
      },
      {
        $match: {
          $or: [
            {
              'otherUser.actualState': 'Colorado',
              'otherUser.actualCountryCode': 'US',
            },
            {
              'otherUser.ipData.region': 'CO',
              'otherUser.ipData.countryCode': 'US',
            },
          ],
        }
      },
      {
        $project: {
          name: '$otherUser.firstName',
          email: '$otherUser.email',
        }
      },
    ]);

    for (const otherUser of matchesFromColorado) {
      await emailLib.sendEmailNotifyingAboutBannedUser(otherUser.email, otherUser.name, user.firstName);
    }
  }
  await socialLib.hideCommentsWhenShadowBanned(user);
}

async function unban(user, by, notes, massUnban) {
  const userId = user._id;
  const bannedReason = user.bannedReason;
  await BannedSource.removeBannedSources(user);
  user.shadowBanned = false;
  user.bannedReason = undefined;
  user.bannedReasons = undefined;
  user.bannedNotes = undefined;
  user.bannedBy = undefined;
  if (user.profileTempBanReason) {
    user.profileTempBanReason = undefined;
    user.profileTempBanReportId = undefined;
    user.profileTempBanInfringingText = undefined;
    user.profileTempBanInfringingPictures = undefined;
    socketLib.sendSocketEvent(user._id, 'undoProfileTempBan', {});
  }
  user.banHistory.push({
    action: 'unban',
    by: by,
    date: Date.now(),
    notes: notes,
  });

  {
    // dismiss all pending appeals
    const appeals = await BanAppeal.find({
      user: user._id,
      decision: { $exists: false },
    });
    for (const appeal of appeals) {
      appeal.decision = 'dismissed';
      appeal.notes = 'user was unbanned before appeal was reviewed';
      await appeal.save();
      user.banHistory.push({
        action: 'appealDismissed',
        date: Date.now(),
        notes: appeal.notes,
      });
    }
  }

  user.banNotice = undefined;
  if (user.verification.updatedAt > Date.now()) {
    user.verification.updatedAt = Date.now();
  }
  user.calculateViewableInDailyProfiles();
  await user.save();

  // update chats
  await Chat.updateMany(
    {
      users: userId,
      groupChat: { $ne: true },
    },
    {
      $pull: {
        bannedUsers: userId,
      },
    },
  );

  // delete from unmatch list
  await deleteShadowBanUnmatchRecord(user);

  // update interest points
  await InterestPoint.updateMany(
    { user: userId },
    { $unset: { shadowBanned: '' } },
  );

  {
    const entries = await InterestPoint.find({
      user: userId,
    });
    for (const entry of entries) {
      await socialLib.updateInterestRanks(entry.interest, entry.language);
    }
  }

  // Remove faceIDs
  let faceIDs = await BannedUser.find({ user: userId }).select('faceIds').lean();
  faceIDs = faceIDs.map(x => x.faceIds || []).flat();

  if (faceIDs.length > 0) {
    await deleteFacesById(faceIDs, UPDATED_BANNED_USERS_COLL_ID);
    await BannedUser.updateMany({ user: userId }, { faceIds: [] });
  }

  // delete from banned file
  await BannedFile.deleteOne({ user: user._id });

  await changeNumberYourTurnChats(userId, 1);

  await socialLib.unhideCommentsWhenShadowUnBanned(user);

  if (!massUnban) {
    await unbanDeviceUsersBlockedForUser(user, by, notes);
  }
}

async function tempBan(user, reason, bannedBy, evidence) {
  if (!user || user._id == chatLib.BOO_SUPPORT_ID || user._id == chatLib.BOO_BOT_ID) {
    return;
  }
  if (user.cannotBeBanned) {
    await emailLib.sendAlertForBanAttemptOnBanExemptUser(`Attempted temp ban on user ${user._id} (name: ${user.firstName}, email: ${user.email}), bannedBy: ${bannedBy}, reason: ${reason}, evidence: ${JSON.stringify(evidence)}`);
    return;
  }
  console.log(`Temp banning user ${user._id} for reason ${reason}`);
  const banDuration = 24;

  if (evidence) {
    if (evidence.messages) {
      // only include messages from the temp banned user
      evidence.messages = evidence.messages.filter(x => x.sender == user._id)
      // filter fields
      evidence.messages = evidence.messages.map(x => ({ _id: x._id, createdAt: x.createdAt, text: x.text, image: x.image, gif: x.gif, audio: x.audio, video: x.video }));
      // make copy of media files and update path
      for (const message of evidence.messages) {
        for (const field of ['image', 'audio', 'video']) {
          if (message[field]) {
            const newPath = await copyEvidenceToUserFolder(message[field], user._id);
            if (newPath) {
              message[field] = newPath;
            }
          }
        }
      }
    }
    if (evidence.post) {
      // filter fields
      evidence.post = {
        _id: evidence.post._id,
        createdAt: evidence.post.createdAt,
        title: evidence.post.title,
        text: evidence.post.text,
        image: evidence.post.image,
        images: evidence.post.images,
        audio: evidence.post.audio,
        gif: evidence.post.gif,
        poll: evidence.post.poll,
      };
      // make copy of media files and update path
      for (const field of ['image', 'audio']) {
        if (evidence.post[field]) {
          const newPath = await copyEvidenceToUserFolder(evidence.post[field], user._id);
          if (newPath) {
            evidence.post[field] = newPath;
          }
        }
      }
      if (evidence.post.images) {
        for (let i = 0; i < evidence.post.images.length; i++) {
          const newPath = await copyEvidenceToUserFolder(evidence.post.images[i].image, user._id);
          if (newPath) {
            evidence.post.images[i].image = newPath;
          }
        }
      }
    }
    if (evidence.comment) {
      // filter fields
      evidence.comment = {
        _id: evidence.comment._id,
        createdAt: evidence.comment.createdAt,
        text: evidence.comment.text,
        image: evidence.comment.image,
        audio: evidence.comment.audio,
        gif: evidence.comment.gif,
      };
      // make copy of media files and update path
      for (const field of ['image', 'audio']) {
        if (evidence.comment[field]) {
          const newPath = await copyEvidenceToUserFolder(evidence.comment[field], user._id);
          if (newPath) {
            evidence.comment[field] = newPath;
          }
        }
      }
    }
  }

  user.tempBanReason = reason;
  user.tempBanEndAt = new Date(Date.now() + banDuration * 3600 * 1000);
  user.tempBanBy = bannedBy;
  user.banHistory.push({
    action: 'tempBan',
    by: bannedBy,
    date: Date.now(),
    reason: reason,
    evidence,
  });

  await markGdprUserIfApplicable(user);

  // add tempBan appeal
  if (user.accountRestrictions || user.isGdprUser) {
    if (!user.accountRestrictions) user.accountRestrictions = {};
    user.accountRestrictions.tempBan = {
      appealStatus: 'allowed',
      evidence: evidence,
    };
  }

  await user.save();

  admin.sendNotification(
    user,
    null,
    '',
    translate_frontend(
      `Your account has been temporarily restricted for {}. You will be able to engage with other souls in {} hours.`,
      user.locale,
      translate_frontend(reason, user.locale),
      banDuration,
      { replaceCurlyPlaceholders: true },
    ),
    null,
    null,
    'negative',
    'temp-ban',
  );
}

async function undoTempBan(user, notes, unbannedBy) {
  if (!user.tempBanEndAt) {
    return;
  }

  user.tempBanReason = undefined;
  user.tempBanEndAt = undefined
  user.tempBanBy = undefined;
  user.banHistory.push({
    action: 'undoTempBan',
    by: unbannedBy?._id,
    date: Date.now(),
    notes: notes,
  });

  if (user.accountRestrictions?.tempBan?.appealStatus == 'pending') {
    const appeal = await TempBanAppeal.findOne({
      user: user._id,
      decision: { $exists: false },
    });
    if (appeal) {
      appeal.decision = 'dismissed';
      appeal.notes = 'user was unbanned before appeal was reviewed'
      await appeal.save();
      user.banHistory.push({
        action: 'tempBanAppealDismissed',
        date: Date.now(),
        notes: 'user was unbanned before appeal was reviewed',
      });
    }
  }

  if(user.accountRestrictions?.tempBan){
    user.accountRestrictions.tempBan = undefined;
  }

  await user.save();
}


async function profileTempBan(user, reason, bannedBy, reportId, keywords = [], pictures = [], override = false) {
  const isAlreadyBanned = !!user.profileTempBanReason;
  if (isAlreadyBanned && !override) return;

  if (user.cannotBeBanned) {
    await emailLib.sendAlertForBanAttemptOnBanExemptUser(`Attempted temp shadow ban on user ${user._id} (name: ${user.firstName}, email: ${user.email}), bannedBy: ${bannedBy}, reason: ${reason}, keywords: ${keywords}, pictures: ${pictures}, reportId: ${reportId}`);
    return;
  }

  const eventPayload = { profileTempBanReason: reason == 'banned keywords found in profile' ? 'Spam, Promotion or Solicitation' : reason };
  let banNotes = reason;

  // Handle keywords
  const hasKeywords = Array.isArray(keywords) && keywords.length > 0;
  if (hasKeywords) {
    const appealedInfringingText = Array.isArray(user.appealedProfileTempBanInfringingText) ? user.appealedProfileTempBanInfringingText : [];
    banNotes += ` - keyword: ${keywords.join(', ')}`;
    const filteredKeywords = keywords.filter(infringingText => !appealedInfringingText.some(appealedText => infringingText.includes(appealedText) || appealedText.includes(infringingText)));
    user.profileTempBanInfringingText = filteredKeywords
    eventPayload.profileTempBanInfringingText = filteredKeywords;
  } else if (override) {
    user.profileTempBanInfringingText = undefined;
  }

  // Handle pictures
  const hasPictures = Array.isArray(pictures) && pictures.length > 0;
  if (hasPictures) {
    const appealedInfringingPictures = Array.isArray(user.appealedProfileTempBanInfringingPictures) ? user.appealedProfileTempBanInfringingPictures : [];
    const filteredPictures = pictures.filter(pic => !appealedInfringingPictures.includes(pic));
    user.profileTempBanInfringingPictures = filteredPictures;
    eventPayload.profileTempBanInfringingPictures = filteredPictures;
  } else if (override) {
    user.profileTempBanInfringingPictures = undefined;
  }

  if(hasPictures || hasKeywords){
    if(!(user.profileTempBanInfringingPictures?.length || user.profileTempBanInfringingText?.length)){
      return;
    }
  }

  // Apply shadow ban only if not already banned
  if (!isAlreadyBanned) {
    await shadowBan(user, bannedBy, 'temp shadow ban due to inappropriate profile', banNotes);
  }

  user.bannedBy = bannedBy;
  user.bannedNotes = banNotes;
  user.profileTempBanReportId = reportId || undefined;
  user.profileTempBanReason = reason;
  await user.save();

  socketLib.sendSocketEvent(user._id, 'profileTempBan', eventPayload);

  await socialLib.hideCommentsWhenShadowBanned(user);
}

async function undoProfileTempBan(user) {
  if (!user.profileTempBanReason && user.bannedReason !== 'temp shadow ban due to inappropriate profile') {
    return;
  }
  await unban(user, null, 'undo temp shadow ban due to inappropriate profile');

  if (user.accountRestrictions?.profileTempBan?.appealStatus == 'pending') {
    const appeal = await ProfileTempBanAppeal.findOne({
      user: user._id,
      decision: { $exists: false },
    });
    if (appeal) {
      appeal.decision = 'dismissed';
      appeal.notes = 'user was unbanned before appeal was reviewed'
      await appeal.save();
      user.banHistory.push({
        action: 'ProfileTempBanAppealDismissed',
        date: Date.now(),
        notes: 'user was unbanned before appeal was reviewed',
      });
    }
  }

  if(user.accountRestrictions?.profileTempBan){
    user.accountRestrictions.profileTempBan = undefined;
    await user.save();
  }

  await socialLib.unhideCommentsWhenShadowUnBanned(user);
}

async function getMessagesSentInChats(chats) {
  if (!chats || !chats.length) {
    return;
  }
  let messages = await Message.find({
    chat: { $in: chats.map(chat => chat._id) },
  })
  .limit(40)
  .sort('-createdAt')
  .lean()
  return messages;
}

async function getMessagesBetweenUsers(user1, user2) {
  // get all chats and messages between the two users
  let chats = await Chat.find({
    userIdHash: Chat.createUserIdHash([user1, user2]),
    groupChat: { $ne: true },
  }, '_id');
  return await getMessagesSentInChats(chats);
}

function isDuplicateOpenaiBan(reportedUser, reason) {
  let found = reportedUser.banHistory.find(x => x.action == 'ban' && x.by == 'openai' && x.reason == reason);
  if (found) {
    return true;
  }
  return false;
}

async function createReport(
  reportedUser,
  reportedById,
  reason,
  comment,
  notes,
  chat,
  fullMatchingImages,
  partialMatchingImages,
  profilePictureSameAsBannedUser,
  autoVerify,
  otherParams = {},
) {
  if (reportedUser.shadowBanned || reportedUser.banned) {
    return;
  }
  if (reportedUser._id == chatLib.BOO_SUPPORT_ID || reportedUser._id == chatLib.BOO_BOT_ID) {
    return;
  }

  await markGdprUserIfApplicable(reportedUser, true);

  if (!reportedById && !reason.some(x => x.includes('Auto-report due to profile keywords')) && !otherParams.isPreemptiveModeration) {
    // if auto-report for the same reason already exists, do not create another one
    let report = await Report.findOne({
      reportedUser: reportedUser._id,
      reason: reason[0],
    });
    if (report) {
      return;
    }
  }

  reason = reason.filter(e => e != 'No Facial Photo');

  let model = Report;
  if (otherParams.isPreemptiveModeration) {
    model = PreemptiveModerationLog;
  }

  let report = new model({
    reportedUser: reportedUser._id,
    reportedBy: reportedById,
    reason,
    comment,
    adminNotes: notes,
    scammerImageDetection: {
      fullMatchingImages,
      partialMatchingImages,
    },
    profilePictureSameAsBannedUser,
  });
  if (chat) {
    report.messages = await getMessages(chat._id, null, null, true);
  }
  if (autoVerify) {
    report.status = 'verified';
    await report.save();
    return report;
  }

  let messages;
  if (otherParams.isPreemptiveModeration) {
    await openai.handleUserReportInappropriateProfile(reportedUser, report, null, otherParams);
  }
  else if (
    report.reason[0]?.includes('Auto-report due to web pose verification')
    || report.reason[0]?.includes('Auto-report due to message keyword')
    || report.reason[0]?.includes('Auto-report: china timezone country mismatch')
    || report.reason[0]?.includes('Auto-report: stripePaymentCountry does not match')
    || report.reason[0]?.includes('Auto-report: stripe currency is Russian ruble')
    || report.reason[0]?.includes('Auto-report: multiple accounts using same stripe payment method')
    || report.reason[0]?.includes('Auto-report: underage keywords detected in bio')
    || ['Underage', 'Catfish/Scammer'].some(x => report.reason.includes(x))
  ) {
    // manual review
  }
  else if (report.reason[0]?.includes('Auto-report due to profile keywords')) {
    await openai.handleUserReportProfileKeywords(reportedUser, report, otherParams);
  }
  else if (report.reason[0]?.includes('Auto-report:')) {
    messages = await Message.find({
      sender: reportedUser._id,
    })
    .limit(40)
    .sort('-createdAt')
    .lean()
    await openai.handleUserGenericAutoReport(reportedUser, report, messages);
  }
  else {
    // user reports
    // Check for number of previous reports within 48 hours
    const reports = await Report.find({ reportedUser: reportedUser._id, reportedBy: { $ne: null }, createdAt: { $gte: moment().subtract(2, 'days').toDate() } });
    const handleWithGPT = reports.length > 0;

    if (report.reason.includes('Catfish/Scammer') && matchesScammerCondition(reportedUser, false)) {
      await shadowBan(reportedUser, null, 'subscribed spam', `report _id: ${report._id}`);
    }
    let includeProfile = report.reason.includes('Inappropriate Profile');
    let includeMessages = report.reason.includes('Inappropriate Messages');
    if (['Spam', 'Underage', 'Catfish/Scammer', 'Other'].some(x => report.reason.includes(x))) {
      includeProfile = true;
      includeMessages = true;
    }
    if (includeMessages) {
      messages = await getMessagesBetweenUsers(reportedUser._id, reportedById);
    }
    if (!messages || !messages.length) {
      includeMessages = false;
    }
    if (includeProfile && includeMessages) {
      await openai.handleUserReportWithProfileAndMessageData(reportedUser, report, messages, handleWithGPT);
    } else if (includeMessages) {
      await openai.handleUserReportInappropriateMessages(reportedUser, report, messages, handleWithGPT);
    } else {
      await openai.handleUserReportInappropriateProfile(reportedUser, report, handleWithGPT);
    }
  }

  if (report.openai && report.openai.decision) {
    if (report.openai.decision == 'shadow_hide' && reportedUser.isGdprUser) {
      report.openai.decision = 'temp_shadow_ban';
    }
    if (report.openai.decision == 'shadow_hide' && report.openai.infringingText?.length > 0) {
      for (const text of report.openai.infringingText) {
        if (reportedUser.firstName.includes(text)) {
          report.openai.decision = 'temp_shadow_ban';
          report.openai.infringingTextFoundInName = true;
          break;
        }
      }
    }
    if (report.openai.decision == 'dismiss') {
      report.status = 'dismissed';
    }
    else if (report.openai.decision == 'shadow_ban') {
      report.status = 'verified';
      if (!isDuplicateOpenaiBan(reportedUser, report.openai.banReason)) {
        await shadowBan(reportedUser, 'openai', report.openai.banReason, `report _id: ${report._id}`);
      }
    }
    else if (report.openai.decision == 'shadow_hide') {
      const user = reportedUser;
      report.status = 'verified';
      if (report.openai.infringingText && report.openai.infringingText.length) {
        if (!user.hiddenProfileText) {
          user.hiddenProfileText = [];
        }
        for (const text of report.openai.infringingText) {
          if (!user.hiddenProfileText.includes(text)) {
            user.hiddenProfileText.push(text);
          }
          for (const field of ['description', 'work', 'education']) {
            if (user[field]) {
              if (!user.originalFields[field]) {
                user.originalFields[field] = user[field];
              }
              user[field] = user[field].replaceAll(text, '');
            }
          }
          if (user.prompts && user.prompts.length) {
            if (!user.originalFields.prompts) {
              user.originalFields.prompts = user.prompts;
            }
            for (let i = 0; i < user.prompts.length; i++) {
              user.prompts[i].answer = user.prompts[i].answer.replaceAll(text, '');
            }
          }
        }
      }
      if (report.openai.infringingPictureKeys && report.openai.infringingPictureKeys.length) {
        user.initOriginalPictures();
        const priorFirstPicture = user.pictures[0];

        if (!user.hiddenPictures) {
          user.hiddenPictures = [];
        }
        const nonVideoPictures = user.pictures.filter(picture => ['.png','.jpeg','.jpg','.gif','.webp'].some(ext => picture.toLowerCase().includes(ext)));
        for (const picture of nonVideoPictures) {
          if (!user.hiddenPictures.includes(picture) && report.openai.infringingPictureKeys.includes(picture)) {
            user.hiddenPictures.push(picture);
          }
        }
        await computePictures(user);

        // if the first profile picture changed, then reverify the user
        const firstPictureChanged = priorFirstPicture != user.pictures[0];
        if (user.isVerified() && firstPictureChanged) {
          const result = await verifyProfilePicture(user);
          if (result == 'manual') {
            await user.setVerificationStatus('reverifying');
          } else if (result == 'reject') {
            await user.setVerificationStatus('rejected');
            user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you, and only you.';
          }
        }
      }
      await user.save();
    }
    else if (report.openai.decision == 'temp_shadow_ban') {
      report.status = 'verified';
      if (!isDuplicateOpenaiBan(reportedUser, report.openai.banReason)) {
        if (report.openai.infringingText?.length) {
          const profileTextParts = [
            reportedUser.firstName,
            reportedUser.education,
            reportedUser.work,
            reportedUser.description,
            ...(reportedUser.prompts?.map(p => p.answer) || []),
          ];
          const text = profileTextParts.filter(Boolean).join(' ');

          report.openai.infringingText = report.openai.infringingText.filter(item => text.includes(item));
        }

        if (report.openai.infringingPictureKeys?.length) {
          report.openai.infringingPictureKeys = report.openai.infringingPictureKeys.filter(item => reportedUser.pictures.includes(item));
        }

        if (report.openai.infringingText?.length || report.openai.infringingPictureKeys?.length) {
          await profileTempBan(reportedUser, report.openai.banReason, 'openai', report._id, report.openai.infringingText, report.openai.infringingPictureKeys);
        } else {
          report.status = 'dismissed';
          report.openai.ban = false;
          report.openai.decision = 'dismiss';
          report.openai.banReason = undefined;
          report.openai.violationLocation = undefined;
          report.openai.infringingText = undefined;
          report.openai.infringingPictureKeys = undefined;
        }
      }
    }
    else if (report.openai.decision == 'temp_ban') {
      report.status = 'verified';
      const priorTempBans = await Report.aggregate([
        {
          $match: {
            reportedUser: reportedUser._id,
            'openai.decision': 'temp_ban',
            createdAt: { $gt: DateTime.utc().minus({ days: 30 }).toJSDate() },
          }
        },
        {
          $group: {
            _id: '$reportedBy',
            count: { $sum: 1 },
          }
        },
      ]);
      const isNew = !priorTempBans.some(x => x._id == reportedById);
      const numReporters = isNew ? priorTempBans.length + 1 : priorTempBans.length;
      if (numReporters >= 5) {
        // if 5 temp bans from 5 different reporters within 30 days, then shadowban
        await shadowBan(reportedUser, 'openai', '5 temp bans from 5 different reporters within 30 days');
      }
      else if (isNew) {
        const evidence = { messages };
        // a user can be temp banned by another user only once
        await tempBan(reportedUser, report.openai.banReason, 'openai', evidence);
      }
    }
    if (report.status == 'verified') {
      const numPriorReports = await Report.countDocuments({
        reportedUser: reportedUser._id,
        reportedBy: reportedById,
        status: 'verified',
      });
      if (!numPriorReports) {
        // schedule a coin award
        report.grantCoinAwardAt = moment().add(1, 'days').toDate();
      }
    }
    if (report.openai.banReason == 'Spam, Promotion or Solicitation' && !reportedUser.shadowBanned) {
      report.status = 'needs review';
    }
  }

  console.log('status', report.status);

  await report.save();

  if (report.status == 'needs review' && !otherParams.isPreemptiveModeration) {
    let MetricName = 'IncomingProfileReports';
    if (report.reason[0]?.includes('Auto-report')) {
      MetricName = 'IncomingProfileAutoReports';
    }
    const params = {
      MetricData: [
        {
          MetricName,
          Value: 1,
          Unit: 'Count',
        },
      ],
      Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
    };
    await cloudwatch.putMetricData(params).promise();

    /*
    await User.incrementMetrics(
      reportedUser._id,
      ['numTotalReports', 'numPendingReports'],
    );

    await sns.publishAlarm(`Report needs review: ${report._id}`);
    */
  }

  /* No longer needed becuase verification picture is being checked in the verification process
  if (!otherParams.isPreemptiveModeration) {
    if (reportedUser.pictures && reportedUser.pictures.length) {
      const imageKey = reportedUser.pictures[0];
      const bannedFace = await findBannedFace(imageKey);
      if (bannedFace) {
        await banDueToBannedFaceFound(reportedUser, 'profile picture during report trigger', imageKey, bannedFace.Face.ExternalImageId);
      }
    }
    if (reportedUser.verification.faceComparisonReferenceImage) {
      const imageKey = reportedUser.verification.faceComparisonReferenceImage;
      const bannedFace = await findBannedFace(imageKey);
      if (bannedFace) {
        await banDueToBannedFaceFound(reportedUser, 'verification picture during report trigger', imageKey, bannedFace.Face.ExternalImageId);
      }
    }
  }
  */

  return report;
}

async function resetNumPendingReports() {
  {
    const res = await User.updateMany({}, { $set: { 'metrics.numPendingReports': 0 } });
    console.log(res);
  }

  const reports = await Report.aggregate(
    [
      {
        '$match': {
          'status': 'needs review'
        }
      }, {
        '$group': {
          '_id': '$reportedUser',
          'count': {
            '$count': {}
          }
        }
      }
    ]
  );

  console.log(reports.length);

  let i = 0;
  let bulk = User.collection.initializeUnorderedBulkOp();
  for (const doc of reports) {
    bulk.find({ _id: doc._id }).update({ $set: { 'metrics.numPendingReports': doc.count } });
    i++;
    if (i % 1000 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = User.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function getScammerPredictionScore(userId) {
  const payload = JSON.stringify({
    userid: userId,
  });
  const params = {
    FunctionName: 'sagemaker-scammer-detection',
    Payload: payload,
  };
  const data = await lambda.invoke(params).promise();
  const parsedData = JSON.parse(data.Payload);
  if (parsedData.predictions) {
    return parsedData.predictions[0][0];
  }
  return 0;
}

async function screenForScammer(user) {

  // disable scammer prediction score
  return;

  if (user.shadowBanned) {
    return;
  }
  const score = await getScammerPredictionScore(user._id);
  if (score > 0.7) {
    await createReport(
      user,
      null,
      ['Auto-report due to scammer prediction score'],
      score,
    );
  }
}

async function preemptiveModerationForUserProfileText(user) {
  if (!constants.runPreemptiveModeration() || !user?.isVerified()) {
    return;
  }
  await createReport(
    user,
    null,
    ['Spam'],
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    { isPreemptiveModeration: true, model: 'gpt-4o-mini' },
  );
}

async function banDueToBannedFaceFound(user, imageSource, imageKey, profilePictureSameAsBannedUser) {
  if (user._id == profilePictureSameAsBannedUser) {
    return;
  }
  await shadowBan(user, null, `banned face found in ${imageSource} (new logic)`, `banned face found in image: ${imageKey}, previously banned user: ${profilePictureSameAsBannedUser}`);
  let bannedFace = new BannedFace({
    user: user._id,
    image: `${constants.IMAGE_DOMAIN}${imageKey}`,
    previouslyBannedUser: profilePictureSameAsBannedUser,
  });
  let previouslyBannedUser = await BannedUser.findOne({user: profilePictureSameAsBannedUser}, 'userData').lean();
  if (previouslyBannedUser) {
    bannedFace.previouslyBannedImages = previouslyBannedUser.userData.pictures.map(x => `${constants.IMAGE_DOMAIN}${x}`);
  }
  await bannedFace.save();
}

async function renameBannedReasonForPhotoVerificationFirstReview() {
  const res = await User.updateMany(
    { bannedReason: { $regex: '\\ \\(banned during photo verification first review\\)' } },
    [
      {
        $set: {
          bannedReason: {
            $replaceAll: {
              input: '$bannedReason',
              find: ' (banned during photo verification first review)',
              replacement: '',
            }
          }
        }
      }
    ]
  );
  console.log(res);
}

async function computePictures(user) {
  if (!user.hiddenPictures || user.hiddenPictures.length == 0) {
    user.pictures = user.originalPictures;
    return;
  }

  // filter out the hidden pictures
  user.pictures = _.difference(user.originalPictures, user.hiddenPictures);
  user.markModified('pictures');

  // if no pictures remaining, ban the user
  if (user.pictures.length == 0 || !user.pictures.some(isValidPicture)) {
    await shadowBan(user, null, 'All pictures shadow hidden');
    await user.save();
    return;
  }

  // if first picture is a video, swap it with a picture
  if (!isValidPicture(user.pictures[0])) {
    const pictureIndex = user.pictures.findIndex(isValidPicture);
    const videoKey = user.pictures[0];
    user.pictures[0] = user.pictures[pictureIndex];
    user.pictures[pictureIndex] = videoKey;
  }

  await user.save();
}

async function hasUserReportedFieldValue(reportingUser, reportCollectionName, fieldName, fieldValue) {
  if (!reportingUser.deviceId) return false;
  const value = fieldName === 'reportedUser' ? fieldValue : new mongoose.Types.ObjectId(fieldValue);
  const result = await User.aggregate([
    { $match: { deviceId: reportingUser.deviceId } },
    { $project: { _id: 1 } },
    {
      $lookup: {
        from: reportCollectionName,
        let: { userId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [`$${fieldName}`, value] },
                  { $eq: ['$reportedBy', '$$userId'] },
                ],
              },
            },
          },
        ],
        as: 'userReports',
      },
    },
    { $match: { 'userReports.0': { $exists: true } } },
  ]);

  return result.length > 0;
}

const preemptiveBanReasons = ['banned file found in profile picture', 'soft ban for unverified users']; // already soft banned users should be eligible for verification
const eligibleForVerification = (bannedReason) => {
  return preemptiveBanReasons.some((reason) => bannedReason?.includes(reason));
};

async function unbanDeletedUser(userId, by, notes) {
  const bannedData = await BannedUser.find({ user: userId }).sort('-userData.bannedDate');
  if (!bannedData.length) return;

  const latestBan = bannedData[0];
  const { userData } = latestBan;

  console.log(`unbanDeletedUser: unbanning user ${userId} data: ${JSON.stringify(bannedData)}`);

  await BannedSource.removeBannedSources(userData);

  const faceIds = bannedData.flatMap(x => x.faceIds || []);
  if (faceIds.length) {
    await deleteFacesById(faceIds, UPDATED_BANNED_USERS_COLL_ID);
  }

  await BannedFile.deleteOne({ user: userId });

  const updatedBanHistory = Array.isArray(userData.banHistory)
    ? [...userData.banHistory]
    : [];

  updatedBanHistory.push({
    action: 'unban',
    by,
    date: new Date().toISOString(),
    notes,
  });

  const updatedUserData = {
    ...userData,
    bannedReason: undefined,
    shadowBanned: false,
    banHistory: updatedBanHistory,
  };

  await BannedUser.updateOne({ _id: latestBan._id }, { $set: { userData: updatedUserData } });

  return updatedUserData;
}

async function unBanAllUsersOnSameDevice(user, by, notes) {
  const sameDeviceUsers = await User.find({
    _id: { $ne: user._id },
    deviceId: user.deviceId,
    shadowBanned: true,
    profileTempBanReason: null,
  });
  if (sameDeviceUsers.length === 0) return;

  for (const bannedUser of sameDeviceUsers) {
    const massUnbanNotes = `${notes} (mass unban with device ID: ${user.deviceId})`;
    await unban(bannedUser, by, massUnbanNotes, true);
    console.log(`unBanAllUsersOnSameDevice: unbanned user ${bannedUser._id} by ${by} with notes: ${massUnbanNotes}`);
  }
}

async function unbanDeviceUsersBlockedForUser(user, by, notes) {
  if (!user?._id || !user?.deviceId) return;

  const sameDeviceUsers = await User.find({
    _id: { $ne: user._id },
    deviceId: user.deviceId,
    shadowBanned: true,
  });
  if (sameDeviceUsers?.length === 0) return;

  const massUnbanNotes = `${notes ?? ""} (mass unban for device ID: ${user.deviceId}) with user: ${user._id}`;
  const allAutoBanned = sameDeviceUsers.every((u) => u?.bannedReason === "Auto-ban: deviceId");
  const usersToUnban = allAutoBanned
    ? sameDeviceUsers
    : sameDeviceUsers.filter((u) => {
      const lastBanHistory = u?.banHistory?.at(-1);
      return lastBanHistory?.action === 'ban' && lastBanHistory?.reason === 'Auto-ban: deviceId' && lastBanHistory?.notes?.includes(`user: ${user._id}`);
    });
  if (usersToUnban.length === 0) return;

  await Promise.all(usersToUnban.map((u) => unban(u, by, massUnbanNotes, true)));
}

async function markGdprUserIfApplicable(user, save) {
  if (!user) return;

  if (
    !user.isGdprUser &&
    (countryLib.gdprCountries.includes(user.ipData?.country) || user.accountRestrictions)
  ) {
    user.isGdprUser = true;
    if (save) await user.save();
  }
}

// injecting the functions to avoid circular dependency
User.setReportLibFunctions(eligibleForVerification, unban, preemptiveModerationForUserProfileText);


module.exports = {
  createReport,
  evaluateBanNotice,
  banReasonMapping,
  banReasonToRuleMapping,
  autoShadowBan,
  shadowBan,
  unban,
  awardUserForReport,
  REPORTS_UNTIL_ACCOUNT_SHADOW_BAN,
  resetNumPendingReports,
  screenForScammer,
  banDueToBannedFaceFound,
  renameBannedReasonForPhotoVerificationFirstReview,
  tempBan,
  undoTempBan,
  profileTempBan,
  undoProfileTempBan,
  computePictures,
  matchesScammerCondition,
  hasUserReportedFieldValue,
  preemptiveModerationForUserProfileText,
  eligibleForVerification,
  unbanDeletedUser,
  markGdprUserIfApplicable,
  unBanAllUsersOnSameDevice,
  getMessagesBetweenUsers,
};
