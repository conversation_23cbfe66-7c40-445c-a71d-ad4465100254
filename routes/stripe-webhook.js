'use strict';

const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const stripe = require('../lib/stripe').stripe;
const User = require('../models/user');
const StripeReceipt = require('../models/stripe-receipt');
const { getExchangeData } = require('../lib/currency-exchange');
const { convertPriceFromStripe } = require('../lib/stripe-prices');
const profilesLib = require('../lib/profiles-v3');
const iapLib = require('../lib/iap');
const reportLib = require('../lib/report');
const locationLib = require('../lib/location');

const endpointSecret = process.env.STRIPE_WEBHOOK_KEY;

function getRevenue(price, currency) {
  price = convertPriceFromStripe(price, currency);

  const conversionRate = getExchangeData()[currency.toUpperCase()] || 0;
  const revenue = price * conversionRate;

  return revenue;
}

async function handleCheckout(event) {
  const session = event.data.object;
  if (session.payment_status != 'paid' || session.status != 'complete') {
    return;
  }

  const sessionWithLineItems = await stripe.checkout.sessions.retrieve(
    session.id,
    {
      expand: ['line_items'],
    }
  );
  const lineItems = sessionWithLineItems.line_items.data;
  console.log('Stripe customer:', session.customer, 'line items:', lineItems);

  const lookup_key = lineItems[0].price.lookup_key;
  const service = 'stripe';
  const purchaseDate = Date.now();
  const transactionId = session.id;
  const price = convertPriceFromStripe(session.amount_total, session.currency);
  const currency = session.currency;
  const revenue = getRevenue(session.amount_total, session.currency);

  const user = await User.findOne({ stripeCustomerId: session.customer });
  if (!user) {
    console.log(`Stripe Receipt Error: Customer not found for : ${JSON.stringify(event)}`);
    return;
  }

  if (lookup_key.includes('coins')) {
    const match = lookup_key.match(/(\d+)_coins/);
    if (!match){
      console.log(`Stripe Checkout Error: no match found for lookup key ${lookup_key}`)
      return
    }
    await iapLib.activateCoinPurchase({
      user,
      service,
      productId: match[0],
      purchaseDate,
      transactionId,
      price,
      currency,
      revenue,
    });
    return;
  }

  if (lookup_key.includes('super_love')) {
    const match = lookup_key.match(/super_love_(\d+)_v1/);
    if (!match){
      console.log(`Stripe Checkout Error: no match found for lookup key ${lookup_key}`)
      return
    }
    await iapLib.activateSuperLikePurchase({
      user,
      service,
      productId: match[0],
      purchaseDate,
      transactionId,
      price,
      currency,
      revenue,
    });
    return;
  }

  if (lookup_key.includes('neurons')) {
    const match = lookup_key.match(/(\d+)_neurons/);
    if (!match){
      console.log(`Stripe Checkout Error: no match found for lookup key ${lookup_key}`)
      return
    }
    await iapLib.activateNeuronPurchase({
      user,
      service,
      productId: match[0],
      purchaseDate,
      transactionId,
      price,
      currency,
      revenue,
    });
    return;
  }

  if (lookup_key.includes('boost')) {
    const match = lookup_key.match(/boost_(\d+)_v1/);
    if (!match){
      console.log(`Stripe Checkout Error: no match found for lookup key ${lookup_key}`)
      return
    }
    await iapLib.activateBoostPurchase({
      user,
      service,
      productId: match[0],
      purchaseDate,
      transactionId,
      price,
      currency,
      revenue,
    });
    return;
  }

  // handle infinity purchases
  const alreadyUsed = await StripeReceipt.findOne({transactionId}, '_id');
  if (alreadyUsed) {
    console.log(`Stripe Receipt already used: ${transactionId}`);
    return;
  }

  const receiptData = await StripeReceipt.create(
    {
      transactionId,
      user: user?._id,
      stripeCustomerId: session.customer,
      revenue,
      product: lineItems[0].price.lookup_key,
      currency: session.currency,
      amount_subtotal: session.amount_subtotal,
      amount_discount: session.total_details.amount_discount,
      amount_tax: session.total_details.amount_tax,
      amount_total: session.amount_total,
      kochava: user?.kochava,
      kochavaNetwork: user?.kochava?.network,
      appsflyer: user?.appsflyer || undefined,
      utm_source: user?.utm_source,
      utm_medium: user?.utm_medium,
      utm_campaign: user?.utm_campaign,
      utm_content: user?.utm_content,
      adset_name: user?.adset_name,
    },
  );

  // check for lifetime purchase
  const lifetimeItem = lineItems.find((lineItem) => lineItem.price.lookup_key.includes('infinity_lifetime'));
  if (lifetimeItem) {
    // 100 years for lifetime purchase
    user.premiumExpiration = moment().add(100, 'years');
    await user.activateInfinitySuperLikes();
  }

  //update revenue metrics
  user.metrics.revenue += revenue;
  user.metrics.stripeRevenue += revenue;
  user.currentDayMetrics.revenue += revenue;

  //update purchase metrics
  user.metrics.madePurchase = true;
  user.metrics.numPurchases += 1;
  user.metrics.numStripePurchases += 1;
  user.currentDayMetrics.numPurchases += 1;

  if (session.total_details.amount_discount > 0) {
    user.metrics.numSalePurchases += 1;
    user.metrics.saleRevenue += revenue;
  }

  await user.save();
}

async function handlePayment(event) {
  const invoice = event.data.object;

  // only process renewal payments
  if (invoice.billing_reason != 'subscription_cycle') {
    return;
  }

  const transactionId = invoice.id;
  const alreadyUsed = await StripeReceipt.findOne({transactionId}, '_id');
  if (alreadyUsed) {
    console.log(`Stripe Receipt already used: ${transactionId}`);
    return;
  }

  const revenue = getRevenue(invoice.amount_paid, invoice.currency);

  const user = await User.findOne({ stripeCustomerId: invoice.customer });

  const receiptData = await StripeReceipt.create(
    {
      transactionId,
      user: user?._id,
      stripeCustomerId: invoice.customer,
      revenue,
      product: invoice.lines.data[0].price.lookup_key,
      currency: invoice.currency,
      isRenewal: true,
      kochava: user?.kochava,
      kochavaNetwork: user?.kochava?.network,
      appsflyer: user?.appsflyer || undefined,
      utm_source: user?.utm_source,
      utm_medium: user?.utm_medium,
      utm_campaign: user?.utm_campaign,
      utm_content: user?.utm_content,
      adset_name: user?.adset_name,
    },
  );

  if (!user) {
    console.log(`Stripe Receipt Error: Customer not found for : ${JSON.stringify(event)}`);
    return;
  }

  //update revenue metrics
  user.metrics.revenue += revenue;
  user.metrics.stripeRevenue += revenue;
  user.currentDayMetrics.revenue += revenue;

  //update purchase metrics
  user.metrics.madePurchase = true;
  user.metrics.numPurchases += 1;
  user.metrics.numStripePurchases += 1;
  user.currentDayMetrics.numPurchases += 1;

  await user.save();
}

async function getInstrumentFingerprint(piId) {
  const pi = await stripe.paymentIntents.retrieve(piId, { expand: ['payment_method'] });
  if (!pi) {
    return null;
  }

  // Ensure we have an object
  let pm = pi.payment_method;
  if (typeof pm === 'string') pm = await stripe.paymentMethods.retrieve(pm);

  // Card (including Apple/Google Pay, Link-card)
  if (pm?.type === 'card') return pm.card?.fingerprint;

  // Bank debits
  if (pm?.type === 'us_bank_account') return pm.us_bank_account?.fingerprint; // ACH, sometimes Link-bank
  if (pm?.type === 'sepa_debit') return pm.sepa_debit?.fingerprint;

  // Fallback via Charge for occasional shape differences
  if (pi.latest_charge) {
    const ch = await stripe.charges.retrieve(pi.latest_charge, {
      expand: ['payment_method_details.card', 'payment_method_details.us_bank_account', 'payment_method_details.sepa_debit'],
    });
    return (
      ch.payment_method_details?.card?.fingerprint ??
      ch.payment_method_details?.us_bank_account?.fingerprint ??
      ch.payment_method_details?.sepa_debit?.fingerprint ??
      null
    );
  }

  return null; // BNPL/redirect wallets etc.
}

async function handlePaymentIntentSucceeded(event) {
  // retrieve and store the payment currency
  const currency = event.data.object.currency;
  const stripeCustomerId = event.data.object.customer;
  if (!stripeCustomerId || !currency) {
    return;
  }
  const user = await User.findOne({ stripeCustomerId });
  if (!user) {
    return;
  }
  if (!user.stripeCurrency) {
    user.stripeCurrency = currency;
    await user.save();
    if (currency == 'rub') {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report: stripe currency is Russian ruble'],
      );
    }
  }

  // retrieve and store the payment fingerprint
  const fp = await getInstrumentFingerprint(event.data.object.id);
  if (fp) {
    await User.updateOne(
      { _id: user._id },
      { $addToSet: { stripePaymentFingerprints: fp } },
    );
    const accountsWithSameFingerprint = await User.find({ stripePaymentFingerprints: fp });
    if (accountsWithSameFingerprint.length >= 3) {
      // simple ascending sort on _id to get a deterministic order for test cases
      accountsWithSameFingerprint.sort((a, b) => (a._id > b._id) - (a._id < b._id));
      for (const account of accountsWithSameFingerprint) {
        await reportLib.createReport(
          account,
          null,
          ['Auto-report: multiple accounts using same stripe payment method'],
          JSON.stringify({ accounts: accountsWithSameFingerprint.map(x => x._id), paymentFingerprint: fp }),
        );
      }
    }
  }
}

async function handleChargeSucceeded(event) {
  const charge = event.data.object;
  if (!charge) {
    return;
  }
  const stripeCustomerId = charge.customer;
  const stripePaymentCountryCode = charge.payment_method_details?.card?.country;
  if (!stripeCustomerId || !stripePaymentCountryCode) {
    return;
  }
  const user = await User.findOne({ stripeCustomerId });
  if (!user) {
    return;
  }
  user.stripePaymentCountry = locationLib.getFullCountryName(stripePaymentCountryCode);
  await user.save();
  if (user.signupCountry && user.stripePaymentCountry != user.signupCountry) {
    await reportLib.createReport(
      user,
      null,
      ['Auto-report: stripePaymentCountry does not match signupCountry'],
      `stripePaymentCountry: ${user.stripePaymentCountry}, signupCountry: ${user.signupCountry}`,
    );
  }
  if (user.actualCountry && user.stripePaymentCountry != user.actualCountry) {
    await reportLib.createReport(
      user,
      null,
      ['Auto-report: stripePaymentCountry does not match actualCountry'],
      `stripePaymentCountry: ${user.stripePaymentCountry}, actualCountry: ${user.actualCountry}`,
    );
  }
}

async function handleSubscription(event) {
  const subscription = event.data.object;
  if (subscription.status != 'active') {
    return;
  }

  const user = await User.findOne({ stripeCustomerId: subscription.customer });
  if (!user) {
    console.log(`Stripe Receipt Error: Customer not found for : ${JSON.stringify(event)}`);
    return;
  }

  user.premiumExpiration = subscription.current_period_end * 1000;
  await user.activateInfinitySuperLikes();
  await user.save();

}

module.exports = function () {

  router.post('/', express.raw({ type: 'application/json' }), asyncHandler(async (req, res, next) => {
    const sig = req.headers['stripe-signature'];
    let event;
    try {
      event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    } catch (err) {
      console.log(`stripe Webhook Error: ${err.message}`);
      res.status(400).send(`Webhook Error: ${err.message}`);
      return;
    }

    console.log('stripe event: ', JSON.stringify(event, null, 2));

    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscription(event/* .data.object */);
        break;
      case 'customer.subscription.updated':
        await handleSubscription(event/* .data.object */);
        break;
      case 'checkout.session.completed':
        await handleCheckout(event);
        break;
      case 'checkout.session.async_payment_succeeded':
        await handleCheckout(event);
        break;
      case 'invoice.payment_succeeded':
        await handlePayment(event);
        break;
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event);
        break;
      case 'charge.succeeded':
        await handleChargeSucceeded(event);
        break;
      default:
        console.log(`stripe unhandled event type ${event.type}.`);
    }
    res.send();
  }));

  return router;
};
