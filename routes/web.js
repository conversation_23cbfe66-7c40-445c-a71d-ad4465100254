const express = require('express');

const router = express.Router();
const mongoose = require('mongoose');
const momentTz = require('moment-timezone');
const asyncHandler = require('express-async-handler');
const httpErrors = require('../lib/http-errors');
const {
  getQuestion, getQuestionInternalLinking, getQuestionsBasedOnProfilesName, getQuestionsBasedOnProfilesSubCategory, getQuestionFeedRouteHandler, getQuestionAllQuestions, getComment, getCommentContext, getSingleCommentById
} = require('../lib/social');
const { DateTime } = require('luxon');
const basic = require('../lib/basic');
const interestLib = require('../lib/interest');
const { handleSearch } = require('../lib/handle-search');
const User = require('../models/user');
const Category = require('../models/category');
const Subcategory = require('../models/subcategory');
const Profile = require('../models/profile');
const LanguageStat = require('../models/language-stat');
const InterestStat = require('../models/interest-stat');
const WebVisitor = require('../models/web-visitor');
const AppVisitor = require('../models/app-visitor');
const databaseLib = require('../lib/database');
const databaseLeadersLib = require('../lib/database-leaders');
const locationLib = require('../lib/location');
const personalityLib = require('../lib/personality');
const emailUnsubLib = require('../lib/email-unsub');
const { getSitemapPaginationPageSize } = require('../lib/constants');
const SitemapPaginatedProfiles = require('../models/sitemap-paginated-profiles');
const SitemapPaginatedSubcategories = require('../models/sitemap-paginated-subcategories');
const SitemapPaginatedCategories = require('../models/sitemap-paginated-categories');
const PersonalityQuizResult = require('../models/personality-quiz-result');
const EnneagramQuizResult = require('../models/enneagram-quiz-result');
const admin = require('../config/firebase-admin');
const profilesLib3 = require('../lib/profiles-v3');
const socialProofLib = require('../lib/social-proof');
const userLib = require('../lib/user');
const partnershipsLib = require('../lib/partnerships');
const { sendSignInEmailSES } = require('../lib/email');
const { executeAggregationWithRetry } = require('../lib/retry-aggregate-query')
const geoip = require('geoip-lite');
const { mbtiBaseLineConfidence, enneagramBaseLineConfidence, zodiacBaseLineConfidence } = require('../lib/baseline-confidence');
const personalityPlaceholders = require('../lib/personalityPlaceholders.json')
const mbtis = ['infp', 'infj', 'enfp', 'enfj', 'intp', 'intj', 'entp', 'entj', 'isfp', 'isfj', 'esfp', 'esfj', 'istp', 'istj', 'estp', 'estj', 'introverts', 'extroverts'];
const enneagrams = ['1w9', '1w2', '2w1', '2w3', '3w2', '3w4', '4w3', '4w5', '5w4', '5w6', '6w5', '6w7', '7w6', '7w8', '8w7', '8w9', '9w8', '9w1'];
const zodiacs = ['aries', 'taurus', 'gemini', 'cancer', 'leo', 'virgo', 'libra', 'scorpio', 'sagittarius', 'capricorn', 'aquarius', 'pisces'];
const EXCLUDE_REGEX = new RegExp("(undefined)|(NULL)|(AI language model)|(OpenAI)|(not possible)|(sorry)|(apologize)|(apologies)|(Unfortunately)|(not appropriate)|(misspelled)|(misspelling)|(no character)|(unable to provide)|(four-paragraph)|(not a known character)|(could not find)|(couldn't find)|(no quote)|(not gained)|(difficult to accurate)|(artificial intelligence)|(language model)|(limited information)", "i");
const moment = require('moment');
const EmailSignup = require('../models/email-signup');
const { verifyRecaptcha, verifyTurnstile } = require('../middleware/recaptcha');
const { getNicheFromCampaign } = require('../lib/niche-campaigns');
const { appsflyerToKochava } = require('../lib/appsflyer')

let recentSignupsCachedTime;
let recentSignupsCachedImages = []

function getMbtiQueryParam(paramMbti) {
  if (paramMbti == 'introverts') {
    return ['INFJ', 'INFP', 'INTJ', 'INTP', 'ISFJ', 'ISFP', 'ISTJ', 'ISTP'];
  }
  if (paramMbti == 'extroverts') {
    return ['ENFJ', 'ENFP', 'ENTJ', 'ENTP', 'ESFJ', 'ESFP', 'ESTJ', 'ESTP'];
  }
  return String(paramMbti).toUpperCase();
}

function getHoroscopeQueryParam(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

function addFilterToQuery(query, filter) {
  if (mbtis.includes(filter)) {
    query.mbti = getMbtiQueryParam(filter);
  }
  if (enneagrams.includes(filter)) {
    query.enneagram = filter;
  }
  if (zodiacs.includes(filter)) {
    query.horoscope = getHoroscopeQueryParam(filter);
  }
}

function calculateVoteFactorAndMaxKey(data) {
  let totalVote = 0;
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      totalVote += data[key];
    }
  }
  let voteFactor = 0.25
  if (totalVote >= 10 && totalVote < 20) {
    voteFactor = 1;
  } else if (totalVote >= 20 && totalVote < 40) {
    voteFactor = 1.25;
  } else if (totalVote >= 40) {
    voteFactor = 1.5;
  }
  return { voteFactor, totalVote };
}

async function calculateConfidenceScore(voteData, userValue, pdbValue, pdbConfidence, type) {
  const { voteFactor, totalVote } = await calculateVoteFactorAndMaxKey(voteData ? voteData : {});
  let confidenceScore = 0;//raw confidence set to 0
  if (!pdbConfidence || pdbConfidence <= 0) {
    pdbValue = null;
  }
  if(!pdbValue && !totalVote){
    if (type == 'mbti') {
      confidenceScore = mbtiBaseLineConfidence[userValue]
        ? mbtiBaseLineConfidence[userValue]
        : 0;
    } else if (type == 'enneagram') {
      confidenceScore = enneagramBaseLineConfidence[userValue]
        ? enneagramBaseLineConfidence[userValue]
        : 0;
    } else if (type == 'horoscope') {
      confidenceScore = zodiacBaseLineConfidence[userValue]
        ? zodiacBaseLineConfidence[userValue]
        : 0;
    }
  }else{
    if (userValue && voteData && voteData[userValue] && totalVote > 0) {
      confidenceScore = (voteData[userValue] / totalVote) * voteFactor * 100;//raw confidence calculated here
    }
  if (pdbValue && userValue && pdbConfidence && pdbConfidence>0 && confidenceScore>=0) {
    confidenceScore = pdbValue.toUpperCase() === userValue.toUpperCase()
      ? (confidenceScore + pdbConfidence) / 2
      : confidenceScore - pdbConfidence * 0.25;
  }
}
return Math.round(Math.min(Math.max(confidenceScore, 0), 100));
}



async function calculateConfidencePercentage(profile) {
  const { vote, pdb_mbti, pdb_enneagram, pdb_horoscope, pdb_confidence, mbti, enneagram, horoscope, birthday } = profile;
  const confidenceScore = { };
  let total = 0;
  let count = 0;
    if(mbti){
  confidenceScore.mbti =await calculateConfidenceScore(
    vote && vote.mbti ? vote.mbti : {},
    mbti,
    pdb_mbti,
    pdb_confidence,
    'mbti'
  );
  total += confidenceScore.mbti;
  count+=1
}
if(enneagram){
  confidenceScore.enneagram =await calculateConfidenceScore(
    vote && vote.enneagram ? vote.enneagram : {},
    enneagram,
    pdb_enneagram,
    pdb_confidence,
    'enneagram'
  );
  total += confidenceScore.enneagram;
  count+=1
}
if(birthday && horoscope){
  confidenceScore.horoscope = 100
  total += confidenceScore.horoscope;
  count += 1
} else if (horoscope) {
  confidenceScore.horoscope =await calculateConfidenceScore(
    vote && vote.horoscope ? vote.horoscope : {},
    horoscope,
    pdb_horoscope,
    pdb_confidence,
    'horoscope'
  );
  total += confidenceScore.horoscope;
  count+=1
}
if (count > 0) {
  confidenceScore.total = Math.round(total / count);
}
return confidenceScore;
}


module.exports = function () {
  router.get('/interest', asyncHandler(interestLib.getInterestRouteHandler));
  router.get('/interests', asyncHandler(async (req, res, next) => {
    res.json({
      interests: interestLib.getAllInterests(),
    });
  }));
  router.get('/popularInterests', asyncHandler(interestLib.getPopularInterestsRouteHandler));
  router.get('/interest/autocomplete', asyncHandler(interestLib.autocompleteRouteHandler));
  router.get('/cached/interest/similar', asyncHandler(interestLib.getSimilarInterestsRouteHandler));
  router.get('/cached/social-proof', asyncHandler(socialProofLib.getSocialProofRouteHandler));

  router.get('/dailyProfiles', asyncHandler(async (req, res, next) => {
    const profiles = await profilesLib3.getWebProfiles(req);
    res.json({
      profiles,
    });
  }));

  router.get('/question', asyncHandler(getQuestion));
  router.get('/question/internalLinking', asyncHandler(getQuestionInternalLinking));
  router.get('/question/profileNameRelated', asyncHandler(getQuestionsBasedOnProfilesName));
  router.get('/question/profileSubcategoryRelated', asyncHandler(getQuestionsBasedOnProfilesSubCategory));
  router.get('/question/feed', asyncHandler(async (req, res, next) => {
    await getQuestionFeedRouteHandler(req, res, next);
  }));
  router.get('/question/allQuestions', asyncHandler(async (req, res, next) => {
    await getQuestionAllQuestions(req, res, next);
  }));

  router.get('/comment', asyncHandler(async (req, res, next) => {
    getComment(req, res, next);
  }));
  router.get('/comment/context', asyncHandler(getCommentContext));
  router.get('/comment/context/v2', asyncHandler(getCommentContext));
  router.get('/comment/single', asyncHandler(getSingleCommentById));

  router.get('/boo', asyncHandler(handleSearch));

  router.get('/database/autocomplete', asyncHandler(async (req, res, next) => {
    const query = req.query.query;
    if (!query) {
      return res.json({
        categories: [],
        subcategories: [],
        profiles: [],
      });
    }

    const pipelineCategory = [
      {
        $search: {
          index: 'database_categories',
          autocomplete: {
            query,
            path: 'name',
          },
        },
      },
      {
        $project: {
          'intros': 0,
        }
      },
      {
        $limit: 10
      },
    ]
    const pipelineSubcategories = [
      {
        $search: {
          index: 'database_subcategories',
          autocomplete: {
            query,
            path: 'name',
          },
        },
      },
      {
        $project: {
          'intros': 0,
        }
      },
      {
        $limit: 10
      },
    ];

    const pipelineProfile = [
      {
        $search: {
          index: 'database_profiles',
          autocomplete: {
            query,
            path: 'name',
          },
        },
      },
      {
        $lookup: {
          from: 'subcategories',
          localField: 'subcategories',
          foreignField: 'id',
          as: 'subcategories',
        },
      },
      {
        $project: {
          'subcategories.intros': 0,
          'subcategories.linkedPillarKeywords': 0,
        }
      },
      {
        $limit: 10
      },
    ]

    const [categories, subcategories, profiles] = await Promise.all([
      executeAggregationWithRetry(Category, pipelineCategory),
      executeAggregationWithRetry(Subcategory, pipelineSubcategories),
      executeAggregationWithRetry(Profile, pipelineProfile)
    ]);


    res.json({
      categories,
      subcategories,
      profiles,
    });
  }));

  router.get('/database/profile', asyncHandler(async (req, res, next) => {
    if (isNaN(req.query.id)) {
      return next(httpErrors.invalidInputError());
    }
    const profile = await Profile
      .findOne({ id: req.query.id }, {
        _id: 1,
        id: 1,
        createdAt: 1,
        createdBy: 1,
        name: 1,
        mbti: 1,
        enneagram: 1,
        horoscope: 1,
        pdb_mbti:1,
        pdb_enneagram:1,
        pdb_horoscope:1,
        pdb_confidence:1,
        description: 1,
        subcategories: 1,
        image: 1,
        imageSource: 1,
        vote: 1,
        'intros.en': 1,
        [`intros.${req.query.locale}`]: 1,
        translatedLanguages: 1,
        countries: 1,
        lastUpdated: 1,
        linkedCategories: 1,
        linkedSubcategories: 1,
        linkedProfiles: 1,
        [`linkedPillarKeywords.${req.query.locale ? req.query.locale : 'en'}`]: 1,
        [`translatedNames.${req.query.locale ? req.query.locale : 'en'}`]: 1,
        birthday: 1,
        imageAttribution: 1,
        linkedKeywordsUpdatedAt: 1,
        linkedKeywordsProcessingStartAt: 1,
        slug: 1,
      })
      .lean();
    if (!profile) {
      return next(httpErrors.notFoundError());
    }
    const passProfileInfo = {
      _id: profile._id,
      id: profile.id,
      intros: profile.intros,
      slug: profile.slug,
      linkedKeywordsUpdatedAt: profile.linkedKeywordsUpdatedAt,
      linkedKeywordsProcessingStartAt: profile.linkedKeywordsProcessingStartAt
    }
    if(profile.mbti && profile.intros?.en) {
      if(profile.intros?.[req.query.locale]?.mbti && EXCLUDE_REGEX.test(profile.intros?.[req.query.locale]?.mbti)) {
        profile.intros[req.query.locale].mbti = personalityPlaceholders[profile.mbti]?.[req.query.locale]?.[profile.id % 15]?.replace("{}", profile.name)?.replace("{}", profile.mbti)
      } else if(EXCLUDE_REGEX.test(profile.intros?.en?.mbti)) {
        profile.intros.en.mbti = personalityPlaceholders[profile.mbti]?.en?.[profile.id % 15]?.replace("{}", profile.name)?.replace("{}", profile.mbti)
      }
    }
    if(profile.enneagram && profile.intros?.en) {
      if(profile.intros?.[req.query.locale]?.enneagram && EXCLUDE_REGEX.test(profile.intros?.[req.query.locale]?.enneagram)) {
        profile.intros[req.query.locale].enneagram = personalityPlaceholders[profile.enneagram]?.[req.query.locale]?.replace("{}", profile.name)?.replace("{}", profile.enneagram)
      } else if(EXCLUDE_REGEX.test(profile.intros?.en?.enneagram)) {
        profile.intros.en.enneagram = personalityPlaceholders[profile.enneagram]?.en?.replace("{}", profile.name)?.replace("{}", profile.enneagram)
      }
    }
    if (profile.createdBy) {
      profile.createdBy = await User.findById(profile.createdBy, 'handle').lean();
    }
    profile.confidence_score = await calculateConfidencePercentage(profile)
    if(profile.birthday && profile.vote) delete profile.vote['horoscope']
    delete profile.pdb_mbti
    delete profile.pdb_enneagram
    delete profile.pdb_horoscope
    delete profile.pdb_confidence
    delete profile.slug
    delete profile.linkedKeywordsUpdatedAt
    delete profile.linkedKeywordsProcessingStartAt
    res.json({
      profile,
    });
    await databaseLib.checkAndUpdateProfileLinkKeywords(passProfileInfo)
  }));

  router.get('/database/subcategoriesWithProfiles', asyncHandler(async (req, res, next) => {
    if (isNaN(req.query.categoryId)) {
      return next(httpErrors.invalidInputError());
    }
    const category = databaseLib.findCategoryById(req.query.categoryId);
    if (!category) {
      return next(httpErrors.notFoundError());
    }

    const { filter } = req.query;
    if (filter && !mbtis.includes(filter) && !enneagrams.includes(filter) && !zodiacs.includes(filter)) {
      return next(httpErrors.invalidInputError());
    }

    const pageSize = databaseLib.getSubcategoryPageSize();
    let page = 1;
    if (req.query.page) {
      if (!isNaN(req.query.page)) {
        page = parseInt(req.query.page);
      }
    }

    const totalSubcategories = await Subcategory
      .countDocuments({ category: category.id });
    const totalPages = Math.ceil(totalSubcategories / pageSize);
    if (page < 1 || page > totalPages) {
      return res.json({ subcategoriesWithProfiles: [] });
    }

    const subcategoriesWithProfiles = await Subcategory
      .find({ category: category.id })
      .sort('-sort')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();

    for (let i = 0; i < subcategoriesWithProfiles.length; i++) {
      subcategoriesWithProfiles[i] = databaseLib.findSubcategoryById(subcategoriesWithProfiles[i].id)
      if(subcategoriesWithProfiles[i] && subcategoriesWithProfiles[i].id){
        const query = { subcategories: subcategoriesWithProfiles[i].id };
        addFilterToQuery(query, filter);
        subcategoriesWithProfiles[i].profiles = await databaseLib.getRandomProfiles(query);
      }
    }

    res.json({
      subcategoriesWithProfiles,
    });
  }));

  router.get('/database/profiles', asyncHandler(async (req, res, next) => {
    if (isNaN(req.query.subcategoryId)) {
      return next(httpErrors.invalidInputError());
    }
    const subcategory = databaseLib.findSubcategoryById(req.query.subcategoryId);
    if (!subcategory) {
      return next(httpErrors.notFoundError());
    }

    const { filter } = req.query;
    if (filter && !mbtis.includes(filter) && !enneagrams.includes(filter) && !zodiacs.includes(filter)) {
      return next(httpErrors.invalidInputError());
    }

    const pageSize = databaseLib.getProfilePageSize();
    let page = 1;
    if (req.query.page) {
      if (!isNaN(req.query.page)) {
        page = parseInt(req.query.page);
      }
    }

    const query = { subcategories: subcategory.id };
    addFilterToQuery(query, filter);

    const totalProfiles = await Profile.countDocuments(query);
    const totalPages = Math.ceil(totalProfiles / pageSize);
    if (page < 1 || page > totalPages) {
      return res.json({ profiles: [] });
    }

    const profiles = await Profile
      .find(query)
      .sort('-sort')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();

    for (const profile of profiles) {
      await databaseLib.populateProfile(profile);
    }

    return res.json({ profiles });
  }));

  router.get('/database/leaders', asyncHandler(async (req, res, next) => {
    const page = parseInt(req.query.page || 0);
    if (isNaN(page)) {
      return next(httpErrors.invalidInputError());
    }
    return res.json(await databaseLeadersLib.getDbLeaders(page));
  }));

  router.put('/quizAnswers', asyncHandler(async (req, res, next) => {
    // Sanitize input
    for (const [key, value] of Object.entries(req.body.answers)) {
      if (isNaN(value)) {
        return next(httpErrors.invalidInputError());
      }
      req.body.answers[key] = Number(value);
    }

    // Process quiz answers
    const personality = personalityLib.processQuiz(req.body.answers);

    // Save the result
    const personalityQuizResult = new PersonalityQuizResult(personality);
    if (req.body.timezone && momentTz.tz.zone(req.body.timezone) != null) {
      personalityQuizResult.timezone = req.body.timezone;
      personalityQuizResult.country = locationLib.getCountryNameFromTimezone(req.body.timezone);
    }
    await personalityQuizResult.save();

    res.json({
      mbti: personalityQuizResult.mbti,
      EI: personalityQuizResult.EI,
      NS: personalityQuizResult.NS,
      FT: personalityQuizResult.FT,
      JP: personalityQuizResult.JP,
      TA: personalityQuizResult.TA,
    });
  }));

  router.put('/enneagramQuizAnswers', asyncHandler(async (req, res, next) => {

    const timezone = req.body.timezone;
    let country;
    if (timezone && momentTz.tz.zone(timezone) != null) {
      country = locationLib.getCountryNameFromTimezone(timezone);
    }

    await EnneagramQuizResult.create({
      quiz: req.body.answers,
      enneagram: req.body.enneagram,
      timezone,
      country,
      deviceId: req.body.deviceId,
    });

    res.json({});
  }));

  router.get('/numQuizzesTaken', asyncHandler(async (req, res, next) => {
    const numQuizzesTaken = await PersonalityQuizResult.estimatedDocumentCount();
    return res.json({
      numQuizzesTaken,
    });
  }));

  router.get('/numUsersPerLanguage', asyncHandler(async (req, res, next) => {
    const metrics = await LanguageStat.findOne({});
    return res.json({
      numUsersPerLanguage: metrics.numUsers,
    });
  }));

  router.get('/numFollowersPerInterest', asyncHandler(async (req, res, next) => {
    const metrics = await InterestStat.findOne({});
    return res.json({
      numFollowersPerInterest: metrics.numFollowers,
    });
  }));

  router.get('/cached/allDatabaseProfiles', asyncHandler(async (req, res, next) => {
    const allDatabaseProfiles = await Profile.find({}, '-_id id name subcategories vote.totalCount').lean();
    return res.json({
      allDatabaseProfiles,
    });
  }));

  router.put('/phoneLogin', asyncHandler(async (req, res, next) => {
    if (process.env.NODE_ENV == 'dev' || process.env.NODE_ENV == 'beta') {
      return res.json({
        allowed: true,
      });
    }
    const user = await User.findOne({ phoneNumber: req.body.phoneNumber });
    if (user && user.shadowBanned) {
      return res.json({
        allowed: false,
      });
    }
    admin.admin.auth()
      .getUserByPhoneNumber(req.body.phoneNumber)
      .then((userRecord) => {
        res.json({
          allowed: true,
        });
      })
      .catch((error) => {
        res.json({
          allowed: false,
        });
      });
  }));

  router.put('/isAuthAllowed', verifyRecaptcha, verifyTurnstile, asyncHandler(async (req, res, next) => {
    const deviceId = req.body.deviceId || req.body.webDeviceId;
    const allowed = await userLib.isAuthAllowed(deviceId, req.body.email, req.ip, 'isAuthAllowed');
    return res.json({
      allowed,
    });
  }));

  router.put('/emailLogin', asyncHandler(async (req, res, next) => {
    const deviceId = req.body.deviceId || req.body.webDeviceId;
    const { email, timezone, locale } = req.body;
    const ip = req.ip;
    // Check if authentication is allowed
    const allowed = await userLib.isAuthAllowed(deviceId, email, ip, 'emailLogin');
    if (!allowed) {
      return res.json({
        allowed,
      });
    }

    // check config to determine email template
    let templateName = 'sign-in-v6';

    try {
      // Generate sign-in link settings
      const actionCodeSettings = {
        url: process.env.NODE_ENV === 'prod' ? 'https://boo.world/account/signup' : 'https://joinbeta.boo.dating/test',
        handleCodeInApp: true,
        iOS: {
          bundleId: 'enterprises.dating.boo',
        },
        android: {
          packageName: 'enterprises.dating.boo',
        },
      };

      // check in email-signup collection and create a record if email does not exists
      await EmailSignup.updateOne(
        { email },
        { $setOnInsert: { email } },
        { upsert: true },
      );

      // Generate sign-in link
      const link = await admin.admin.auth().generateSignInWithEmailLink(email, actionCodeSettings);

      // Prepare email options and send email
      const emailOptions = {
        templateName,
        email,
        locale: locale || 'en',
        timezone,
        signInLink: link,
      };

      await sendSignInEmailSES(emailOptions);
    } catch (error) {
      console.log(`Send login email from backend error: ${error.message}`);
      return res.json({
        allowed,
        useLegacy: true,
      });
    }

    return res.json({
      allowed,
      useLegacy: false,
    });
  }));

  router.put('/visitor', asyncHandler(async (req, res, next) => {
    let visitor = await WebVisitor.findOne({webDeviceId: req.body.webDeviceId});
    if (!visitor) {
      visitor = new WebVisitor({
        webDeviceId: req.body.webDeviceId,
        locale: req.body.locale,
        platform: req.body.platform,
      });
      visitor.initConfig();
      await visitor.save();
    }

    let rv = {};

    // experimental configs
    if (visitor.config) {
      for (const [key, value] of Object.entries(visitor.config)) {
        rv[key] = value;
      }
    }

    // finalized configs
    rv.social_proof_signed_up = true;
    rv.show_login_timer = false;
    rv.redirect_mobile_to_app_store_v2 = true;
    rv.get_app_button = false;
    rv.move_universe_intro_paragraph = true;
    rv.web_103 = true;

    return res.json({
      config: rv,
    });
  }));

  router.put('/appVisitor', asyncHandler(async (req, res, next) => {
    const { appVersion, deviceId, locale, appsflyer_id } = req.body;
    let {kochava, appsflyer} = req.body

    if (appsflyer) {
      if (appsflyer_id) {
          appsflyer.appsflyer_id = appsflyer_id;
      }
      const result = await appsflyerToKochava(appsflyer);
      if (result) {
          kochava = result; // Set kochava only if result is truthy
      }
    } else if (!appsflyer && appsflyer_id) {
        appsflyer = {
            appsflyer_id: appsflyer_id
        };
    }

    let visitor = await AppVisitor.findOne({ deviceId });
    if (!visitor) {
      visitor = new AppVisitor({ appVersion, deviceId, kochava, locale, appsflyer });
      visitor.initConfig();
      await visitor.save();
    }

    let rv = {};

    // experimental configs
    if (visitor.config) {
      for (const [key, value] of Object.entries(visitor.config)) {
        rv[key] = value;
      }
    }

    // finalized configs
    rv.use_bunny_cdn = true;
    rv.app_531 = true;
    rv.app_554 = false;
    rv.app_595 = false;
    rv.app_834 = true;

    const { niche, subniche } = getNicheFromCampaign(visitor);
    if (niche == 'gaming') {
      rv.app_459 = true;
    }
    if (niche == 'anime') {
      rv.app_483 = true;
    }

    // detect country
    let countryCode;
    const geo = geoip.lookup(req.ip);
    if (geo) {
      countryCode = geo.country;
    }

    return res.json({
      config: rv,
      countryCode,
    });
  }));

  router.put('/unsubscribe-email', asyncHandler(async (req, res, next) => {
    const { email, hash } = req.query;

    const user = await User.findOne({ email });
    if (!user) {
      return next(httpErrors.notFoundError());
    }

    const unsubHash = emailUnsubLib.unsubHash(user._id, email);
    if (unsubHash != hash) {
      return next(httpErrors.notFoundError());
    }

    user.pushNotificationSettings.email = false;
    await user.save();

    return res.json({});
  }));

  router.get('/partnerships', asyncHandler(async (req, res, next) => {
    let partnerships = partnershipsLib.getPartnershipData(req.ip);
    res.json({
      partnerships,
    });
  }));

  router.get('/recentSignups', asyncHandler(async (req, res, next) => {
    if (recentSignupsCachedImages.length == 0 || moment().diff(recentSignupsCachedTime, 'hours') >= 1) {
      recentSignupsCachedTime = Date.now();
      const users = await User.aggregate([
        {
          $sort: { createdAt: -1 }
        },
        {
          $match: {
            shadowBanned: false,
            pictures: { $exists: true, $ne: [] }
          }
        },
        {
          $project: {
            firstPicture: { $arrayElemAt: ["$pictures", 0] }, // Retrieve the first picture
          }
        },
        {
          $limit: 100
        }
      ]);

      // Transform the result into a flat array of strings
      recentSignupsCachedImages = users.map(user => `${user.firstPicture}`);
    }

    res.json({
      images: recentSignupsCachedImages,
    });
  }))

  router.get('/cached/profiles/database', asyncHandler(async (req, res, next) => {
    const locale = req.query.locale || 'en';
    const filter = req.query.filter || 'none';
    const country = req.query.country
    const type = req.query.type || 'all';
    const results = {};

    const allCategories = await databaseLib.getCategories();
    let selectedCategories = [];
    if (type === 'all') {
      selectedCategories = allCategories;
    } else if (type === 'famous') {
      const famousPeopleIds = [1, 2, 6, 9, 10, 11, 12];
      selectedCategories = allCategories.filter(cat => famousPeopleIds.includes(cat.id));
    } else if (type === 'fictional') {
      const fictionalIds = [3, 4, 5, 7, 8];
      selectedCategories = allCategories.filter(cat => fictionalIds.includes(cat.id));
    }

    const usedProfileIds = [];
    for (const category of selectedCategories) {
      const profiles = await databaseLib.getProfiles({
        subcategories: await databaseLib.findPopularSubcategoryIds(category.id),
        filter: filter,
        locale: locale,
        country: country,
        categoryId: category.id,
        excludeProfileIds: usedProfileIds
      });
      results[category.id] = profiles || [];
      const newProfileIds = (profiles || []).map(p => p.id);
      usedProfileIds.push(...newProfileIds);
    }

    res.json({
      categories: results
    });
  }));

  router.get('/cached/profiles/category', asyncHandler(async (req, res, next) => {
    const locale = req.query.locale || 'en';
    const filter = req.query.filter || 'none';
    const country = req.query.country

    if (!req.query.categoryId) return next(httpErrors.invalidInputError());
    const categoryId = parseInt(req.query.categoryId);
    let category = databaseLib.findCategoryById(categoryId)
    if (!category) {
      category = await Category.findOne({ id: categoryId }).select({ countries: 1, id: 1 }).lean()
      if (!category) return next(httpErrors.invalidInputError());
    }
    const availableCountries = category.countries
    const profiles = await databaseLib.getProfiles({
      subcategories: await databaseLib.findPopularSubcategoryIds(categoryId),
      country: country,
      filter: filter,
      locale: locale,
      categoryId: categoryId,
      availableCountries: availableCountries,
    });

    res.json({
      profiles
    });
  }));

  router.get('/cached/profiles/subcategory', asyncHandler(async (req, res, next) => {
    const locale = req.query.locale || 'en'
    const filter = req.query.filter || 'none'
    const country = req.query.country
    const size = req.query.size || 24
    const page = Math.max(req.query.page || 1, 1)

    if(!req.query.subCategoryId) return next(httpErrors.invalidInputError());
    const subcategoryId = parseInt(req.query.subCategoryId);
    let subCategory = databaseLib.findSubcategoryById(subcategoryId)
    if(!subCategory) {
      subCategory = await Subcategory.findOne({ id: subcategoryId }).select({ countries: 1, category: 1, id: 1 }).lean()
      if(!subCategory) return next(httpErrors.invalidInputError());
    }
    const availableCountries = subCategory.countries
    const profiles = await databaseLib.getProfiles({
      subcategories: subcategoryId,
      country: country,
      filter: filter,
      locale: locale,
      limit: parseInt(size) + 1, // added 1 profile to check if next page exist or not
      skip: (parseInt(page) - 1) * size,
      categoryId: subCategory.category,
      availableCountries: availableCountries,
    });

    res.json({
      hasMore: profiles.length > size,
      profiles: profiles.slice(0, size),
    });

  }));

  router.get('/cached/profiles/subcategory/sitemap', asyncHandler(async (req, res, next) => {
    const page = Math.max(req.query.page || 1, 1)
    if(!req.query.subCategoryId) return next(httpErrors.invalidInputError());
    const subcategoryId = parseInt(req.query.subCategoryId);
    if(!subcategoryId) return next(httpErrors.invalidInputError());

    let sitemapData = await SitemapPaginatedSubcategories.findOne({ subcategoryId, pageNo: page }).lean()

    if(!sitemapData) {
      sitemapData = await SitemapPaginatedSubcategories.findOne({ subcategoryId }).select({ pageNo: 1, totalPages: 1 }).lean()
      if(!sitemapData) return next(httpErrors.invalidInputError());
      return res.json({
        profiles: [],
        totalPages: sitemapData.totalPages || 0,
      });
    }

    const profiles = await Profile
      .find({ subcategories: subcategoryId, id: { $in: sitemapData.profilesId } })
      .select({ _id: 0, id: 1, name: 1, slug: 1 })
      .sort({ name: 1 })
      .lean()

    return res.json({
      profiles: profiles,
      totalPages: sitemapData.totalPages,
    });

  }));

  router.get('/cached/profiles/category/sitemap', asyncHandler(async (req, res, next) => {
    const page = Math.max(req.query.page || 1, 1)
    if(!req.query.categoryId) return next(httpErrors.invalidInputError());
    const categoryId = parseInt(req.query.categoryId);

    if(!categoryId) return next(httpErrors.invalidInputError());

    let sitemapData = await SitemapPaginatedCategories.findOne({ categoryId: categoryId, pageNo: page }).lean()

    if(!sitemapData) {
      sitemapData = await SitemapPaginatedCategories.findOne({ categoryId: categoryId }).select({ pageNo: 1, totalPages: 1 }).lean()
      if(!sitemapData) return next(httpErrors.invalidInputError());
      return res.json({
        subcategories: [],
        totalPages: sitemapData.totalPages || 0,
      });
    }

    const subcategories = await Subcategory
      .find({ category: categoryId, id: { $in: sitemapData.subcategoriesId } })
      .select({ _id: 0, id: 1, name: 1, slug: 1 })
      .sort({ name: 1 })
      .lean()

    return res.json({
      subcategories: subcategories,
      totalPages: sitemapData.totalPages,
    });

  }));

  router.get('/cached/profiles/sitemap', asyncHandler(async (req, res, next) => {
    const page = Math.max(req.query.page || 1, 1)
    const pageSize = getSitemapPaginationPageSize('profiles');

    let sitemapData = await SitemapPaginatedProfiles.findOne({ pageNo: page }).lean()

    if(!sitemapData) {
      sitemapData = await SitemapPaginatedProfiles.findOne({}).lean()
      if(!sitemapData) return next(httpErrors.invalidInputError());
      return res.json({
        profiles: [],
        totalPages: sitemapData.totalPages || 0,
      });
    }

    const profiles = await Profile
      .find({ id: { $gte: sitemapData.startId } })
      .select({ _id: 0, id: 1, name: 1, slug: 1, subcategories: 1, categories: 1 })
      .sort({ id: 1 })
      .limit(pageSize)
      .lean()

    for (const profile of profiles) {
      await databaseLib.populateProfile(profile);
    }

    return res.json({
      profiles: profiles,
      totalPages: sitemapData.totalPages,
    });

  }));

  router.get('/cached/profiles/profile', asyncHandler(async (req, res, next) => {
    const locale = req.query.locale || 'en'
    const profileId = req.query.profileId
    if(!profileId) return next(httpErrors.invalidInputError());
    let profile = await Profile
    .findOne({ id: profileId })
    .select({ _id: 1, id: 1, categories: 1, subcategories: 1, enneagram: 1, mbti: 1, horoscope: 1, linkedProfiles: 1, detailPageProfilesCache: 1 })
    .lean()
    if(!profile) return next(httpErrors.invalidInputError());
    const profiles = await databaseLib.getDetailPagesRelatedProfiles(profile,locale)
    res.json({
      relatedProfiles: profiles.relatedProfiles,
      mbtiProfiles: profiles.mbtiProfiles,
      enneagramProfiles: profiles.enneagramProfiles,
      horoscopeProfiles: profiles.horoscopeProfiles,
    })
  }));

  router.get('/category', asyncHandler(async (req, res, next) => {
    const { slug, filter, locale = 'en', country } = req.query;

    if (!slug || (!country && !filter)) {
      return next(httpErrors.invalidInputError());
    }

    const category = await databaseLib.getCategoryInfo(slug, filter, locale, country);

    res.json({
      category
    });
  }));

  router.get('/subcategory', asyncHandler(async (req, res, next) => {
    const { category, slug } = req.query;

    if (!category || !slug) {
      return next(httpErrors.invalidInputError());
    }

    const subcategory = await databaseLib.getSubcategoryInfo(category, slug);
    res.json({ subcategory });
  }));

  router.get('/cached/subcategory/paragraph', asyncHandler(async (req, res, next) => {
    const { type, category, subcategory, locale, filter } = req.query;

    if (!type) {
      return next(httpErrors.invalidInputError());
    }

    const result = await databaseLib.getSubcategoryParagraph(type, category, subcategory, filter, locale);
    res.json(result);
  }));

  router.get('/cached/country/paragraph', asyncHandler(async (req, res, next) => {
    const { type, category, subcategory, locale, country, filter } = req.query;

    if (!type) {
      return next(httpErrors.invalidInputError());
    }

    const result = await databaseLib.getCountryParagraph(type, category, subcategory, country, filter, locale);
    res.json(result);
  }));

  router.get('/error', asyncHandler(async (req, res, next) => {
    throw httpErrors.applicationError('TEST ERROR');
  }));

  return router;
};
