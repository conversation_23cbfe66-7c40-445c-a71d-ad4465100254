#!/usr/bin/env bash
set -euo pipefail

# Get Beanstalk's app staging directory
APP_STAGING_DIR="$(
  /opt/elasticbeanstalk/bin/get-config platformconfig -k AppStagingDir
)"

# Environment variables (set in Beanstalk config)
CRON_ENABLED="${CRON_ENABLED:-false}"      # Enable or disable cron setup
CRON_VARIANT="${CRON_VARIANT:-}"           # e.g. "worker" or "worker-data-export"

cd "$APP_STAGING_DIR"

# Remove any existing cron.yaml to avoid stale schedules
rm -f cron.yaml

if [[ "$CRON_ENABLED" == "true" ]]; then
  SRC="${CRON_VARIANT}/cron.yaml"

  if [[ -n "$CRON_VARIANT" && -f "$SRC" ]]; then
    cp "$SRC" cron.yaml
    echo "[cron] Selected cron file: $SRC"
  else
    echo "[cron] WARNING: CRON_ENABLED=true but $SRC not found. No cron.yaml will be deployed."
  fi
else
  echo "[cron] CRON is disabled in this environment; skipping cron.yaml."
fi
