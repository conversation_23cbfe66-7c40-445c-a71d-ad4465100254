const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { app, mongoose, validImagePath, initSocket, getSocketPromise, destroySocket, waitMs } = require('./common');
const User = require('../models/user');
const Chat = require('../models/chat');
const Message = require('../models/message');
const UserMetadata = require('../models/user-metadata');
const BoostMetric = require('../models/boost-metric');
const coinsConstants = require('../lib/coins-constants');
const iapHelper = require('./helper/iap');
const { updateUserScores } = require('../lib/score');
const { fakeAdminMessaging } = require('./stub');
const premiumLib = require('../lib/premium');
const iap = require('in-app-purchase');
const PurchaseReceipt = require('../models/purchase-receipt');
const axios = require('axios');
const basic = require('../lib/basic');
const { DateTime } = require('luxon');
const chatLib = require('../lib/chat');

let durationMinutes;

describe('boost', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.50' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', uid)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', uid)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', uid)
        .send({
          friends: ['female', 'male'],
          personality: ['ISTP'],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', uid)
        .send({
          mbti: 'ISTP',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', uid)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
    }
  });

  it('basic functionality', async () => {
    // set user 1 to have a high decayed score
    user = await User.findById('1');
    user.scores.decayedScore = 80;

    await user.save();
    user = await User.findById('1');
    user.scores.decayedScore2 = 1;
    await user.save();
    user = await User.findById('2');
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    expect(user.scores.decayedScore2).to.equal(3);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(1);
    expect(metrics[0].numLikesReceived).to.equal(1);
    expect(metrics[0].numLocalActionsReceived).to.equal(1);
    expect(metrics[0].numLocalLikesReceived).to.equal(1);
    expect(metrics[0].numBoosts).to.equal(1);
    expect(metrics[0].coinsSpent).to.equal(200);

    user = await User.findById('2');
    expect(user.metrics.numBoostUsed).to.equal(1);
  });

  it('boost pop up on init', async () => {

    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 3)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // use boosts
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 0)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // init before boosts expire
    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.numLikesReceivedDuringBoost).to.equal(undefined);

    // receive 3 likes when boost is active
    for(let uid = 1; uid <= 3; uid++){
        res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '0' });
      expect(res.status).to.equal(200);
    }

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('0');
    expect(metrics[0].numActionsReceived).to.equal(3);
    expect(metrics[0].numLikesReceived).to.equal(3);
    expect(metrics[0].numLocalActionsReceived).to.equal(3);
    expect(metrics[0].numLocalLikesReceived).to.equal(3);
    expect(metrics[0].numBoosts).to.equal(1);
    expect(metrics[0].coinsSpent).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numBoostUsed).to.equal(1);

    user = await User.findById('0');
    user.boostExpiration = new Date();
    await user.save();


    // init after boosts expire
    //expect numLikesReceivedDuringBoost = 3
    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.body.numLikesReceivedDuringBoost).to.equal(3);

  })

  it('get boost pop up', async () => {
    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

    res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 3)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // use boosts
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 0)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // init before boosts expire
    res = await request(app)
        .get('/v1/user/boostPopup')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.numLikesReceivedDuringBoost).to.equal(undefined);

    // receive 3 likes when boost is active
    for(let uid = 1; uid <= 3; uid++){
        res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '0' });
      expect(res.status).to.equal(200);
    }

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('0');
    expect(metrics[0].numActionsReceived).to.equal(3);
    expect(metrics[0].numLikesReceived).to.equal(3);
    expect(metrics[0].numLocalActionsReceived).to.equal(3);
    expect(metrics[0].numLocalLikesReceived).to.equal(3);
    expect(metrics[0].numBoosts).to.equal(1);
    expect(metrics[0].coinsSpent).to.equal(200);

    user = await User.findById('0');
    expect(user.metrics.numBoostUsed).to.equal(1);

    user = await User.findById('0');
    user.boostExpiration = new Date();
    await user.save();


    // init after boosts expire
    //expect numLikesReceivedDuringBoost = 3
    res = await request(app)
        .get('/v1/user/boostPopup')
        .set('authorization', 0)
        .send({ appVersion: '1.13.53' });
      expect(res.status).to.equal(200);
      expect(res.body.numLikesReceivedDuringBoost).to.equal(3);

  })

  it('distance loop still takes precedence', async () => {
    // set user 2 far away (GB)
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 51.75,
        longitude: 0,
      });
    expect(res.status).to.equal(200);

    // clear pending report
    user = await User.findById('2');
    user.metrics.numPendingReports = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');
  });

  it('expiration', async () => {
    // set user 1 to have a high decayed score
    user = await User.findById('1');
    user.scores.decayedScore = 80;

    user = await User.findById('1');
    user.scores.decayedScore2 = 1;
    await user.save();
    user = await User.findById('2');
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.boost.durationMinutes).to.equal(30);
    const expirationDate = new Date(res.body.user.boostExpiration).getTime();
    const duration = (expirationDate - Date.now()) / 60000;
    assert(duration > 29 && duration < 30);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    // let boost expire
    user = await User.findById('2');
    user.boostExpiration = new Date();
    await user.save();
    await updateUserScores();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.boostExpiration).to.equal();

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(0);
    expect(metrics[0].numLikesReceived).to.equal(0);
    expect(metrics[0].numLocalActionsReceived).to.equal(0);
    expect(metrics[0].numLocalLikesReceived).to.equal(0);
    expect(metrics[0].postBoostNumActionsReceived).to.equal(1);
    expect(metrics[0].postBoostNumLikesReceived).to.equal(1);

    // let more time pass
    user = await User.findById('2');
    user.boostExpiration = new Date(2000,1,1);
    await user.save();

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(0);
    expect(metrics[0].numLikesReceived).to.equal(0);
    expect(metrics[0].numLocalActionsReceived).to.equal(0);
    expect(metrics[0].numLocalLikesReceived).to.equal(0);
    expect(metrics[0].postBoostNumActionsReceived).to.equal(1);
    expect(metrics[0].postBoostNumLikesReceived).to.equal(1);
  });

  it('increase duration to 60 minutes for 1.11.14', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.11.14' });
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.boost.durationMinutes).to.equal(60);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    const expirationDate = new Date(res.body.user.boostExpiration).getTime();
    const duration = (expirationDate - Date.now()) / 60000;
    assert(duration > 59 && duration < 60);
  });

  it('level up', async () => {
    // check coin product
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.coinProducts.levelUp.price).to.equal(coinsConstants.levelUpCost);
    expect(res.body.coinProducts.levelUp.durationMinutes).to.equal(24 * 60);
    expect(res.body.user.boostExpiration).to.equal();

    // set user 1 to have a high decayed score
    user = await User.findById('1');
    user.scores.decayedScore = 80;

    user = await User.findById('1');
    user.scores.decayedScore2 = 1;
    await user.save();
    user = await User.findById('2');
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // not enough coins
    res = await request(app)
      .put('/v1/coins/levelUp')
      .set('authorization', 2)
      .send({ price: coinsConstants.levelUpCost });
    expect(res.status).to.equal(403);

    // now activate boost for user 2
    doc = await UserMetadata.findOne({ user: '2' });
    doc.coins = coinsConstants.levelUpCost;
    await doc.save();

    res = await request(app)
      .put('/v1/coins/levelUp')
      .set('authorization', 2)
      .send({ price: coinsConstants.levelUpCost });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.boostExpiration).to.not.equal();
    const expirationDate = new Date(res.body.user.boostExpiration).getTime();
    const duration = (expirationDate - Date.now()) / 60000;
    assert(duration > 24 * 60 - 1 && duration < 24 * 60);

    doc = await UserMetadata.findOne({ user: '2' });
    expect(doc.coins).to.equal(0);

    // send like, then check boost metrics
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numActionsReceived).to.equal(1);
    expect(metrics[0].numLikesReceived).to.equal(1);
    expect(metrics[0].numLocalActionsReceived).to.equal(1);
    expect(metrics[0].numLocalLikesReceived).to.equal(1);

    user = await User.findById('2');
    expect(user.metrics.numLiftOffUsed).to.equal(1);
  });

  it('boosted users should come before new users', async () => {
    user = await User.findById('1');
    expect(user.scores.decayedScore2).to.equal(2);

    user = await User.findById('2');
    expect(user.scores.decayedScore2).to.equal(2);
    user.scores.decayedScore2 = 0;
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('1');
    expect(res.body.profiles[1]._id).to.equal('2');

    // now activate boost for user 2
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    user = await User.findById('2');
    expect(user.scores.decayedScore2).to.equal(3);

    user = await User.findOne({ _id: 0 });
    user.recentRecommendations = [];
    await user.save();

    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(2);
    expect(res.body.profiles[0]._id).to.equal('2');
    expect(res.body.profiles[1]._id).to.equal('1');
  });

  it('stack boosts', async () => {
    clock = sinon.useFakeTimers();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.11.14' });
    expect(res.status).to.equal(200);

    doc = await UserMetadata.findOne({ user: '2' });
    doc.coins = 1000;
    await doc.save();

    // activate two boosts
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 2)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    const boostExpiration = 2 * 60 * 60 * 1000;

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.user.boostExpiration).getTime()).to.equal(boostExpiration);

    const metrics = await BoostMetric.find();
    expect(metrics.length).to.equal(1);
    expect(metrics[0].user).to.equal('2');
    expect(metrics[0].numBoosts).to.equal(2);
    expect(metrics[0].coinsSpent).to.equal(400);
    expect(metrics[0].durationMinutes).to.equal(120);
    expect(new Date(metrics[0].boostExpiration).getTime()).to.equal(boostExpiration);
  });
});

describe('APP-904: Boosts Sale Trigger on Infinity Purchase', () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.102' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i.toString());
      user.config.app_904 = true;
      await user.save();
    }
  });

  it('should trigger sale when user is eligible', async () => {
    let user = await User.findById(0);
    expect(user.config.app_904).to.equal(true);
    expect(premiumLib.isPremium(user)).to.equal(false);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(undefined);

    // Purchase infinity premium
    let res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: iapHelper.getValidGoogleReceipt('boo_infinity_12_months', Date.now(), 'mock-transaction-id'),
      });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(undefined);
    expect(user.shouldTriggerBoostsSaleNextInit).to.equal(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    user = await User.findById(0);
    expect(user.boostsSaleTriggerDate).to.be.instanceOf(Date);
    expect(user.boostsFlashSaleEndDate).to.be.instanceOf(Date);
    expect(DateTime.fromJSDate(user.boostsSaleTriggerDate) <= DateTime.utc().plus({ seconds: 1 })).to.equal(true);
    expect(DateTime.fromJSDate(user.boostsFlashSaleEndDate) >= DateTime.utc().plus({ hours: 6 }).minus({ seconds: 1 })).to.equal(true);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(1);
    expect(user.shouldTriggerBoostsSaleNextInit).to.equal(true);

    // if not received popup displayed event, will set the flag again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    // sending event should clear the flag
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ opened_boosts_sale_triggered_popup_infinity: true });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.shouldTriggerBoostsSaleNextInit).to.equal(undefined);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);
  });

  it('should not trigger sale for renewal purchases', async () => {
    let user = await User.findById(0);
    expect(user.config.app_904).to.equal(true);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(undefined);

    // Initial purchase
    let receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-transaction-id');
    receipt.purchaseToken = 'ocpeipimpkfiedhdhhhfgchh.AO-J1Ox6Z9Ug4uaWLizHE9-bG3gdmoGU2-Y32R3xTMG84_h0ZC_bIRuGh7ZtPaMkF2L0gHD2uNMOih5U23LZk_hEjitrG9hDuBf6s0PBlNe5MoTOylkBM_8';

    let res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    user = await User.findById(0);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(1);
    expect(premiumLib.isPremium(user)).to.equal(true);

    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ opened_boosts_sale_triggered_popup_infinity: true });
    expect(res.status).to.equal(200);

    const googleRenewalRtdnBody = {
      message: {
        data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbk5vdGlmaWNhdGlvbiI6eyJ2ZXJzaW9uIjoiMS4wIiwibm90aWZpY2F0aW9uVHlwZSI6MiwicHVyY2hhc2VUb2tlbiI6Im9jcGVpcGltcGtmaWVkaGRoaGhmZ2NoaC5BTy1KMU94Nlo5VWc0dWFXTGl6SEU5LWJHM2dkbW9HVTItWTMyUjN4VE1HODRfaDBaQ19iSVJ1R2g3WnRQYU1rRjJMMGdIRDJ1Tk1PaWg1VTIzTFprX2hFaml0ckc5aER1QmY2czBQQmxOZTVNb1RPeWxrQk1fOCIsInN1YnNjcmlwdGlvbklkIjoiYm9vX2luZmluaXR5XzFfbW9udGgifX0=',
        messageId: '3060979050628477',
        message_id: '3060979050628477',
        publishTime: '2021-09-16T07:50:12.994Z',
        publish_time: '2021-09-16T07:50:12.994Z',
      },
      subscription: 'projects/boo-dating-prod/subscriptions/renewals',
    };

    receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-renewal-transaction-id');
    receipt.purchaseToken = 'ocpeipimpkfiedhdhhhfgchh.AO-J1Ox6Z9Ug4uaWLizHE9-bG3gdmoGU2-Y32R3xTMG84_h0ZC_bIRuGh7ZtPaMkF2L0gHD2uNMOih5U23LZk_hEjitrG9hDuBf6s0PBlNe5MoTOylkBM_8';

    sinon.stub(iap, 'validateOnce').callsFake((_, __, cb) => cb(null, receipt));

    res = await request(app).post('/google-renewal').send(googleRenewalRtdnBody);
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    expect(user.shouldTriggerBoostsSaleNextInit).to.equal(undefined);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);

    user = await User.findById(0);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(1);

    const purchaseReceipts = await PurchaseReceipt.find(
      { subscriptionId: receipt.purchaseToken },
      null,
      { sort: { purchaseDate: 1 } },
    );
    expect(purchaseReceipts.length).to.equal(2);
  });

  it('should not trigger sale if last triggered within 14 days', async () => {
    const clock = sinon.useFakeTimers({ now: Date.now(), toFake: ['Date'] });

    let user = await User.findById(0);
    expect(user.config.app_904).to.equal(true);

    // Initial purchase triggers sale
    let receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-transaction-id');
    let res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);

    // should send field to trigger sale on next init
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    // Cancel event simulation
    const googleCancelRtdnBody = {
      message: {
        data: 'eyJ2ZXJzaW9uIjoiMS4wIiwicGFja2FnZU5hbWUiOiJlbnRlcnByaXNlcy5kYXRpbmcuYm9vIiwiZXZlbnRUaW1lTWlsbGlzIjoiMTYzMTc3ODYxMjg5MSIsInN1YnNjcmlwdGlvbiI6eyJub3RpZmljYXRpb25UeXBlIjozfX0=',
      },
    };
    res = await request(app).post('/google-renewal').send(googleCancelRtdnBody);
    expect(res.status).to.equal(200);

    clock.tick(7 * 3600 * 1000);

    // Purchase again within cooldown — should NOT trigger sale
    receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-second-transaction-id');
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);

    // Fast-forward 14 days — should now trigger sale
    clock.tick(14 * 24 * 3600 * 1000);

    await PurchaseReceipt.deleteMany({});

    receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-third-transaction-id');
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    user = await User.findById(0);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(2);
  });

  it('should not trigger sale if config is disabled', async () => {
    let user = await User.findById(0);
    user.config.app_904 = false;
    await user.save();

    const receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-transaction-id');
    let res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({ receipt });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);

    user = await User.findById(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(undefined);
    expect(user.shouldTriggerBoostsSaleNextInit).to.equal(undefined);
  });

  it('should handle previous version and 14-day cooldown edge cases', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ appVersion: '1.13.96' });
    expect(res.status).to.equal(200);

    let user = await User.findById(4);
    expect(user.config.app_904).to.equal(undefined);

    // Older version should not trigger sale
    let receipt = iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-transaction-id');
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 4)
      .send({ receipt });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);

    // Simulate cooldown boundary
    const clock = sinon.useFakeTimers({ now: Date.now(), toFake: ['Date'] });
    user.appVersion = '1.13.101';
    user.config.app_904 = true;
    user.boostsSaleTriggerDate = DateTime.utc().toJSDate();
    await user.save();

    // Just before 14 days — should NOT trigger
    clock.tick(14 * 24 * 3600 * 1000 - 1000);
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 4)
      .send({
        receipt: iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'second-transaction-id'),
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);

    // At 14-day mark — should trigger sale
    clock.tick(1000);
    await PurchaseReceipt.deleteMany({});

    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 4)
      .send({
        receipt: iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'third-transaction-id'),
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    user = await User.findById(4);
    expect(DateTime.fromJSDate(user.boostsSaleTriggerDate) >= DateTime.utc().minus({ seconds: 1 })).to.equal(true);
    expect(DateTime.fromJSDate(user.boostsFlashSaleEndDate) >= DateTime.utc().plus({ hours: 6 }).minus({ seconds: 1 })).to.equal(true);
    expect(user.metrics.numBoostsSalesTriggeredInfinity).to.equal(1);

    clock.restore();
  });

  it('should update events when user opens the triggered popup', async () => {
    // Trigger sale first
    let res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: iapHelper.getValidGoogleReceipt('boo_infinity_1_month', Date.now(), 'mock-transaction-id'),
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    // App notifies popup opened
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ opened_boosts_sale_triggered_popup_infinity: true });
    expect(res.status).to.equal(200);

    const user = await User.findById(0);
    expect(user.events.opened_boosts_sale_triggered_popup_infinity).to.equal(1);
  });

  it('should not send bot message for triggered sale', async () => {
    const clock = sinon.useFakeTimers({ now: Date.now(), toFake: ['Date'] });

    let user = await User.findById('0').lean();
    expect(user.config.app_904).to.equal(true);
    expect(user.booMessages).to.eql({});

    // fast-forward 3 days
    clock.tick(3 * 24 * 3600 * 1000);

    let res = await request(app)
      .get('/v1/config/boosts')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.boosts_discount).to.equal(50);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(undefined);
    await waitMs(100);

    // should receive bot message after initApp
    let chat = await Chat.find({ users: { $all: [chatLib.BOO_BOT_ID, '0'] } });
    expect(chat.length).to.equal(1);

    let messages = await Message.find({ chat: chat[0]._id }).sort({ createdAt: 1 });
    expect(messages.length).to.equal(2);
    expect(messages[0].text.includes('Save 50% on Boost for the next 6 hours')).to.equal(true);

    clock.tick(24 * 3600 * 1000);

    // Purchase infinity premium
    res = await request(app)
      .put('/v1/user/purchasePremium')
      .set('authorization', 0)
      .send({
        receipt: iapHelper.getValidGoogleReceipt('boo_infinity_12_months', Date.now(), 'mock-transaction-id'),
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ isAppStart: true });
    expect(res.status).to.equal(200);
    expect(res.body.triggerBoostsSalePopup).to.equal(true);

    // should not receive another bot message
    messages = await Message.find({ chat: chat[0]._id }).sort({ createdAt: -1 });
    expect(messages.length).to.equal(2);

    clock.restore();
  });
});

it('app 366 false / not set', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.59' });
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  user.numBoosts = 5
  await user.save();


  // activate first boost pop up show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  expectedDate = new Date(new Date().getTime() + 60 * 60 * 1000);
  truncatedExpectedDate = new Date(expectedDate.setSeconds(0, 0)).toISOString();
  actualDate = new Date(res.body.boostExpiration);
  truncatedActualDate = new Date(actualDate.setSeconds(0, 0)).toISOString();

  expect(truncatedActualDate).to.equal(truncatedExpectedDate);

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(60);
  console.log('boost duration : ',user.boostExpiration)

  // activate second boost pop up not show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  expectedDate = new Date(new Date().getTime() + 120 * 60 * 1000);
  truncatedExpectedDate = new Date(expectedDate.setSeconds(0, 0)).toISOString();
  actualDate = new Date(res.body.boostExpiration);
  truncatedActualDate = new Date(actualDate.setSeconds(0, 0)).toISOString();

  expect(truncatedActualDate).to.equal(truncatedExpectedDate);
  console.log('res : ',res.body)

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(120);
  console.log('boost duration : ',user.boostExpiration)

  // activate third boost pop up not show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  console.log('res : ',res.body)

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(180);
  expectedDate = new Date(new Date().getTime() + 180 * 60 * 1000);
  truncatedExpectedDate = new Date(expectedDate.setSeconds(0, 0)).toISOString();
  actualDate = new Date(res.body.boostExpiration);
  truncatedActualDate = new Date(actualDate.setSeconds(0, 0)).toISOString();
  console.log('boost duration : ',user.boostExpiration)

  // set the boost to expire
  user.boostExpiration = new Date()
  user.save()

  // activate fourth boost after third is end pop up show
  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 2)
  expect(res.status).to.equal(200);
  expect(res.body.boostedPopup).to.be.undefined;
  console.log('res : ',res.body)

  user = await User.findById('2')
  expect(user.boostDurationMinutes).to.equal(60);
  console.log('boost duration : ',user.boostExpiration)
});

it('admin increment boost', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.50'});
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.admin = true;
  user.adminPermissions = { support: true };
  res = await user.save();

  res = await request(app)
    .patch('/v1/admin/incrementBoosts')
    .set('authorization', 1)
    .send({
      user: '0',
      numBoosts: 3,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.50'});
  expect(res.status).to.equal(200);
  expect(res.body.user.numBoosts).to.equal(3);

  res = await request(app)
    .patch('/v1/admin/incrementBoosts')
    .set('authorization', 1)
    .send({
      user: '0',
      numBoosts: 10,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.50'});
  expect(res.status).to.equal(200);
  expect(res.body.user.numBoosts).to.equal(13);

  // check boost transactions
  res = await request(app)
    .get('/v1/admin/user/boostTransactions')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  console.log(res.body.transactions);
  expect(res.body.transactions.length).to.equal(2);
  expect(res.body.transactions[0].transactionAmount).to.equal(10);
  expect(res.body.transactions[0].newBalance).to.equal(13);
  expect(res.body.transactions[0].description).to.equal('admin incremented boosts');
  expect(res.body.transactions[1].transactionAmount).to.equal(3);
  expect(res.body.transactions[1].newBalance).to.equal(3);
  expect(res.body.transactions[1].description).to.equal('admin incremented boosts');
});

describe('APP-903: Boosts Sale Trigger', () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.101' });
      expect(res.status).to.equal(200);
    }
  });

  it('should trigger sale when user is eligible', async () => {
    let user = await User.findById(0);
    expect(user.config.app_903).to.equal(true);
    expect(user.numBoosts).to.equal(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    // Purchase boosts
    const receipt = iapHelper.getValidAppleReceipt('boosts_5_v1', Date.now());
    let res = await request(app)
      .put('/v1/boosts/purchase')
      .set('authorization', 0)
      .send({
        receipt,
        price: 3400,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.numBoosts).to.equal(5);
    user.numBoosts = 1;
    await user.save();

    // Use last boost should trigger sale
    const socket = await initSocket('0');
    const socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await socketPromise;
    expect(res).to.eql({
      event: `opened_boosts_sale_triggered_popup`,
    });

    user = await User.findById(0);
    expect(user.numBoosts).to.equal(0);
    expect(user.boostsSaleTriggerDate instanceof Date).to.equal(true);
    expect(user.boostsFlashSaleEndDate instanceof Date).to.equal(true);
    expect(DateTime.fromJSDate(user.boostsSaleTriggerDate) <= DateTime.utc().plus({ seconds: 1 })).to.equal(true);
    expect(DateTime.fromJSDate(user.boostsFlashSaleEndDate) >= DateTime.utc().plus({ hours: 6 }).minus({ seconds: 1 })).to.equal(true);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(1);

    await destroySocket(socket);
  });

  it('should not trigger sale if user has remaining boosts', async () => {
    let user = await User.findById(0);
    expect(user.config.app_903).to.equal(true);
    expect(user.numBoosts).to.equal(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    // Purchase boosts
    const receipt = iapHelper.getValidAppleReceipt('boosts_5_v1', Date.now());
    let res = await request(app)
      .put('/v1/boosts/purchase')
      .set('authorization', 0)
      .send({
        receipt,
        price: 3400,
        currency: 'JPY',
      });
    expect(res.status).to.equal(200);

    // Use boosts — should NOT trigger sale
    const socket = await initSocket('0');
    const socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    let saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise(resolve => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(false);

    user = await User.findById(0);
    expect(user.numBoosts).to.equal(4); // used 1
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    await destroySocket(socket);
  });

  it('should not trigger sale if last triggered within 14 days', async () => {
    const clock = sinon.useFakeTimers({ now: Date.now(), toFake: ['Date'] });

    let user = await User.findById(0);
    expect(user.config.app_903).to.equal(true);
    expect(user.numBoosts).to.equal(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    // Add boosts
    user = await User.findById(0);
    user.numBoosts = 1;
    await user.save();

    // Use last boost — should trigger sale
    const socket = await initSocket('0');
    let socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    let res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await socketPromise;
    expect(res).to.eql({
      event: `opened_boosts_sale_triggered_popup`,
    });

    user = await User.findById(0);
    expect(user.numBoosts).to.equal(0);
    expect(DateTime.fromJSDate(user.boostsSaleTriggerDate) >= DateTime.utc().minus({ seconds: 1 })).to.equal(true);
    expect(DateTime.fromJSDate(user.boostsFlashSaleEndDate) >= DateTime.utc().plus({ hours: 6 }).minus({ seconds: 1 })).to.equal(true);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(1);

    // add another boost
    user.numBoosts = 1;
    await user.save();

    // Use another boost before 14 days — should NOT trigger sale
    socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');
    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    let saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise(resolve => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(false);

    user = await User.findById(0);
    expect(user.numBoosts).to.equal(0);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(1);

    // Fast forward 14 days, next boost should trigger sale
    user.numBoosts = 1;
    await user.save();
    clock.tick(14 * 24 * 3600 * 1000);

    socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');
    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await socketPromise;
    expect(res).to.eql({
      event: `opened_boosts_sale_triggered_popup`,
    });

    user = await User.findById(0);
    expect(user.numBoosts).to.equal(0);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(2);

    await destroySocket(socket);
    clock.restore();
  });

  it('should not trigger sale if coins used for boosts or config is disabled', async () => {
    let user = await User.findById(0);
    expect(user.config.app_903).to.equal(true);
    expect(user.numBoosts).to.equal(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    user.numBoosts = 1;
    await user.save();

    // Use boosts purchased with coins — should NOT trigger sale
    const socket = await initSocket('0');
    let socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    let res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    let saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise((resolve) => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(false);

    user = await User.findById(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    // Config disabled
    user.config.app_903 = false;
    user.numBoosts = 1;
    await user.save();

    // Use boosts — should NOT trigger sale because config disabled
    socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');
    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise((resolve) => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(false);

    user = await User.findById(0);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(undefined);

    await destroySocket(socket);
  });

  it('should handle edge cases: previous version, 14-day cooldown', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 4)
      .send({ appVersion: '1.13.96' });
    expect(res.status).to.equal(200);

    // User with previous version (pre 1.13.101)
    let user = await User.findById(4);
    expect(user.config.app_903).to.equal(undefined);
    user.numBoosts = 1;
    user.boostsSaleTriggerDate = undefined;
    user.boostsFlashSaleEndDate = undefined;
    await user.save();

    let socket = await initSocket('4');
    let socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 4);
    expect(res.status).to.equal(200);

    let saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise(resolve => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(false);

    user = await User.findById(4);
    expect(user.boostsSaleTriggerDate).to.equal(undefined);
    expect(user.boostsFlashSaleEndDate).to.equal(undefined);

    // Attempt trigger just before 14 days — should NOT trigger
    const clock = sinon.useFakeTimers({ now: Date.now(), toFake: ['Date'] });
    socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    // add config
    user.appVersion = '1.13.101';
    user.config.app_903 = true;
    user.numBoosts = 1;
    user.boostsSaleTriggerDate = DateTime.utc().toJSDate();
    await user.save();

    clock.tick(14 * 24 * 3600 * 1000 - 1000);
    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 4);
    expect(res.status).to.equal(200);

    saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise(resolve => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(false);

    // Trigger at exact 14-day boundary — should trigger sale
    user = await User.findById(4);
    user.numBoosts = 1;
    await user.save();
    socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    clock.tick(1000); // advance 1 second to reach 14 days
    res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 4);
    expect(res.status).to.equal(200);

    saleTriggered = false;
    await Promise.race([
      socketPromise.then(() => { saleTriggered = true; }),
      new Promise(resolve => setTimeout(resolve, 200)),
    ]);
    expect(saleTriggered).to.equal(true);

    user = await User.findById(4);
    expect(DateTime.fromJSDate(user.boostsSaleTriggerDate) >= DateTime.utc().minus({ seconds: 1 })).to.equal(true);
    expect(DateTime.fromJSDate(user.boostsFlashSaleEndDate) >= DateTime.utc().plus({ hours: 6 }).minus({ seconds: 1 })).to.equal(true);
    expect(user.metrics.numBoostsSalesTriggered).to.equal(1);

    await destroySocket(socket);
    clock.restore();
  });

  it('should update events when user opens the superlike triggered popup', async () => {
    let user = await User.findById(0);
    user.numBoosts = 1;
    await user.save();

    const socket = await initSocket('0');
    const socketPromise = getSocketPromise(socket, 'triggerBoostsFlashSale');

    let res = await request(app)
      .put('/v1/boosts/use')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    await socketPromise;

    // App sends "opened_boosts_sale_triggered_popup" event
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 0)
      .send({ opened_boosts_sale_triggered_popup: true });
    expect(res.status).to.equal(200);

    // Verify event field incremented
    user = await User.findById(0);
    expect(user.events.opened_boosts_sale_triggered_popup).to.equal(1);

    await destroySocket(socket);
  });
});

it('boost live activity token', async () => {
  const sendStub = sinon.stub(axios, 'post').callsFake((url, body, options) => new Promise((resolve, reject) => {
    console.log(`Override axios.post() ${url} ${JSON.stringify(body,null,2)} ${JSON.stringify(options,null,2)}`);
    resolve({ data: 'success' });
  }));

  clock = sinon.useFakeTimers();

  // create two users who both use boost
  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
    expect(res.status).to.equal(200);

    doc = await UserMetadata.findOne({ user: i.toString() });
    doc.coins = 1000;
    await doc.save();

    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', i)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);
    expect(new Date(res.body.user.boostExpiration).getTime()).to.equal(30 * 60 * 1000);
  }

  // set fcm and activity tokens on user 0 only
  res = await request(app)
    .put('/v1/user/fcmToken')
    .set('authorization', 0)
    .send({
      fcmToken: 'fcmToken',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/boosts/activityToken')
    .set('authorization', 0)
    .send({ activityToken: 'activityToken' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.boostActivityToken).to.equal('activityToken');

  user = await User.findById('1');
  expect(user.boostActivityToken).to.equal();

  // trigger worker job - no message sent because boost not expired yet
  res = await request(app)
    .post('/v1/worker/endBoostLiveActivity')
  expect(res.status).to.equal(200);

  assert(sendStub.notCalled);

  // tick clock to expire boost
  clock.tick(30 * 60 * 1000);

  // trigger worker job - only one message sent to user 0
  res = await request(app)
    .post('/v1/worker/endBoostLiveActivity')
  expect(res.status).to.equal(200);

  sinon.assert.calledOnce(sendStub);
  sinon.assert.calledWith(
    sendStub,
    'https://fcm.googleapis.com/v1/projects/boo-dating-beta/messages:send',
    {
      message: {
        'token': 'fcmToken',
        'apns': {
          'live_activity_token': 'activityToken',
          'headers': {
            'apns-priority': '10'
          },
          'payload': {
            'aps': {
              'timestamp': 1800,
              'dismissal-date': 1800,
              'event': 'end',
              'content-state': {},
            }
          }
        }
      },
    },
    {
      headers: { Authorization: 'Bearer mockToken', 'Content-Type': 'application/json' },
      timeout: 10000
    }
  );
  sendStub.resetHistory();

  // activity token should be cleared after message sent
  user = await User.findById('0');
  expect(user.boostActivityToken).to.equal();

  user = await User.findById('1');
  expect(user.boostActivityToken).to.equal();

  // trigger worker job again - no message sent because no users have activity token
  res = await request(app)
    .post('/v1/worker/endBoostLiveActivity')
  expect(res.status).to.equal(200);

  assert(sendStub.notCalled);
});

describe('APP-893', () => {
  beforeEach(async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    for (let i = 0; i <= 10; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: i % 2 === 0 ? '1.13.102' : '1.13.98' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      expect(user.appVersion).to.equal(i % 2 === 0 ? '1.13.102' : '1.13.98');
      expect(user.config.app_895).to.equal(i % 2 === 0 ? true : undefined);

      if (i % 2 === 0) {
        user.verification.status = 'verified';
        await user.save();
      }
    }
  });

  it('creates or increments hourly view entry', async () => {
    let res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    let user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(await chatLib.calculateBoostEffectiveness(user)).to.equal(0);

    // view from user 3, unverified, should not increase count
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 3)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(await chatLib.calculateBoostEffectiveness(user)).to.equal(0);

    // view from user 2, should increase count
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 2)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(2);

    // shadow banned users should not increase count
    user = await User.findById('4');
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 4)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(2);
  });

  it('removes views older than 25h but keeps cutoff hour', async () => {
    let clock = sinon.useFakeTimers({
      now: Date.now(),
    });

    let res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    const initHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    let user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(initHourKey)).to.equal(1);

    clock.tick(3600000); // 1 hour later

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const oneHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(2);
    expect(user.metrics.numViewsReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(initHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(oneHourKey)).to.equal(1);

    clock.tick(3600000 * 22); // 22 hours later

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 4)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 6)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const twentyThreeHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(3);
    expect(user.metrics.numViewsReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(initHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(twentyThreeHourKey)).to.equal(2);

    clock.tick(2 * 3600000);
    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(3);
    expect(user.metrics.numViewsReceivedHourly.has(initHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(initHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(twentyThreeHourKey)).to.equal(2);

    // manually add more entry so total entry becomes 26
    for (let i = 25; i <= 48; i++) {
      const hourKey = DateTime.utc().minus({ hours: i }).startOf('hour').toISO({ suppressMilliseconds: true });
      user.metrics.numViewsReceivedHourly.set(hourKey, 0);
      await user.save();
    }

    // new like should remove initHourKey
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 8)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });
    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(3); // initHourKey was removed
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(twentyThreeHourKey)).to.equal(2);
    expect(user.metrics.numViewsReceivedHourly.has(initHourKey)).to.equal(false);
    expect(user.metrics.numViewsReceivedHourly.get(initHourKey)).to.equal(undefined);

    // sending like/viewing from same user again should not increase count
    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 8)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.size).to.equal(3); // initHourKey was removed
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(oneHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(oneHourKey)).to.equal(1);
    expect(user.metrics.numViewsReceivedHourly.has(twentyThreeHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(twentyThreeHourKey)).to.equal(2);

    clock.restore();
  });

  it('handles multiple views at the same time', async () => {
    const currentHourKey = DateTime.utc().startOf('hour').toISO({ suppressMilliseconds: true });

    // send 5 concurrent likes from different verified users
    const senders = [2, 4, 6, 8, 10];
    await Promise.all(
      senders.map(uid =>
        request(app)
          .patch('/v1/user/sendLike')
          .set('authorization', uid)
          .send({ user: '1' }),
      ),
    );

    // verify count is 5 (not 1, not corrupted by race)
    let user = await User.findById('1');
    expect(user.metrics.numViewsReceivedHourly.has(currentHourKey)).to.equal(true);
    expect(user.metrics.numViewsReceivedHourly.get(currentHourKey)).to.equal(5);
  });

  it('tracks boost views correctly when boost is active', async () => {
    await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });

    let res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 2)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    let boosts = await BoostMetric.find({ user: '1' });
    expect(boosts.length).to.equal(1);
    expect(boosts[0].numActionsReceived).to.equal(1);
    expect(boosts[0].numLikesReceived).to.equal(1);
    expect(boosts[0].numViewsReceived).to.equal(1);

    res = await request(app)
      .get('/v1/user/profileDetails')
      .set('authorization', 4)
      .query({ user: '1' });
    expect(res.status).to.equal(200);

    boosts = await BoostMetric.find({ user: '1' });
    expect(boosts.length).to.equal(1);
    expect(boosts[0].numActionsReceived).to.equal(1);
    expect(boosts[0].numLikesReceived).to.equal(1);
    expect(boosts[0].numViewsReceived).to.equal(2);
  });

  it('returns 0 effectiveness if boost is active', async () => {
    await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });

    let user = await User.findById('1');
    let result = await chatLib.calculateBoostEffectiveness(user);
    expect(result).to.equal(0);

    // returns 0 if boost expiration is invalid
    user = await User.findById('1');
    user.boostExpiration = null;
    await user.save();

    result = await chatLib.calculateBoostEffectiveness(user);
    expect(result).to.equal(0);

    // less than 24 entry
    user = await User.findById('1');
    user.metrics.numViewsReceivedHourly = new Map();
    user.metrics.numViewsReceivedHourly.set(DateTime.utc().set({ millisecond: 0 }).toISO({ suppressMilliseconds: true }), 1);
    await user.save();

    result = await chatLib.calculateBoostEffectiveness(user);
    expect(result).to.equal(0);
  });

  it('calculates boost effectiveness: view increases > 1', async () => {
    const clock = sinon.useFakeTimers({
      now: Date.now(),
    });

    // Fill hourly data for 24 hours with low counts
    let user = await User.findById('1');
    user.metrics.numViewsReceivedHourly = new Map();
    let totalViews = 0;
    for (let i = 24; i > 0; i--) {
      const hourKey = DateTime.utc().minus({ hours: i }).startOf('hour').toISO({ suppressMilliseconds: true });
      user.metrics.numViewsReceivedHourly.set(hourKey, 2);
      totalViews += 2;
    }
    await user.save();

    // Start boost
    let res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 1)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // Add 6 views during boost
    for (let uid of [2, 4, 6, 8, 10, 0]) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '1' });
      expect(res.status).to.equal(200);
    }

    // Move time forward to end boost
    clock.tick(61 * 60 * 1000);

    user = await User.findById('1');
    expect(await chatLib.calculateBoostEffectiveness(user)).to.equal(4);

    // should add boost effectiveness
    const metrics = await BoostMetric.find({ user: '1' }).sort({ boostExpiration: -1 });
    expect(metrics.length).to.equal(1);
    expect(metrics[0].numViewsReceived).to.equal(6);
    expect(metrics[0].boostEffectiveness).to.equal(4);

    clock.restore();
  });

  it('should send popup notification and socket event', async () => {
    sinon.stub(axios, 'post').callsFake((url, body, options) => new Promise((resolve, reject) => {
      console.log(`Override axios.post() ${url} ${JSON.stringify(body,null,2)} ${JSON.stringify(options,null,2)}`);
      resolve({ data: 'success' });
    }));

    const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake(
      (params) =>
        new Promise((resolve, reject) => {
          console.log('Fake messaging().send() ', JSON.stringify(params, null, 2));
          if (params.token === 'invalidToken') {
            return reject(new Error('Fake error'));
          }
          resolve({ response: 'success' });
        }),
    );

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 11)
      .send({ appVersion: '1.13.102' });

    const clock = sinon.useFakeTimers({
      now: Date.now(),
      toFake: ['Date'],
    });

    // Fill hourly data for 24 hours with low counts
    let user = await User.findById('11');
    expect(user.boostEffectiveness).to.equal(undefined);
    expect(user.config.app_893).to.equal(true);
    user.metrics.numViewsReceivedHourly = new Map();
    for (let i = 24; i > 0; i--) {
      const hourKey = DateTime.utc().minus({ hours: i }).startOf('hour').toISO({ suppressMilliseconds: true });
      user.metrics.numViewsReceivedHourly.set(hourKey, 2);
    }
    await user.save();

    // Start boost
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 11)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // Add 6 views during boost
    for (let uid of [2, 4, 6, 8, 10, 0]) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '11' });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 11)
      .send({
        fcmToken: 'fcmToken',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/boosts/activityToken')
      .set('authorization', 11)
      .send({ activityToken: 'activityToken' });
    expect(res.status).to.equal(200);

    // Move time forward to end boost
    clock.tick(60 * 60 * 1000);

    let socket = await initSocket(11);
    let socketPromise = getSocketPromise(socket, 'triggerBoostSuccessPopup');

    res = await request(app)
      .post('/v1/worker/notifyBoostEffectiveness');
    expect(res.status).to.equal(200);

    const params = {
      token: 'fcmToken',
      notification: {
        title: 'Your Boost was a success!',
        body: 'You were seen by 4 more people in the last 60 minutes!',
      },
      apns: {
        payload: {
          aps: {
            sound: 'general.wav',
            badge: 1,
          },
        },
      },
      android: {
        priority: 'high',
        notification: {
          channelId: 'general',
          sound: 'general.wav',
        },
      },
      data: {
        openPage: 'boostSuccessPopup',
        boostEffectiveness: '4',
      },
      fcmOptions: { analyticsLabel: 'boost-success' },
    };

    sinon.assert.calledOnce(sendStub);
    sinon.assert.calledWith(sendStub, params);

    // should also send socket event
    res = await socketPromise;
    expect(res).to.eql({
      openPage: 'boostSuccessPopup',
      boostEffectiveness: '4',
    });
    await destroySocket(socket);

    // popup pending should be added in user profile
    user = await User.findById('11');
    expect(user.boostEffectiveness).to.equal(4);

    // initApp should have boostEffectiveness field
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 11);
    expect(res.status).to.equal(200);
    expect(res.body.user.boostEffectiveness).to.equal(undefined);
    expect(res.body.boostEffectiveness).to.equal(4);

    // send popup shown event
    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 11)
      .send({ boost_success_popup_seen: true });
    expect(res.status).to.equal(200);

    user = await User.findById('11');
    expect(user.metrics.numSuccessfulBoosts).to.equal(1);
    expect(user.events.boost_success_popup_seen).to.equal(1);
    expect(user.boostEffectiveness).to.equal(undefined);

    // initApp should no longer have boostEffectiveness field
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 11);
    expect(res.status).to.equal(200);
    expect(res.body.boostEffectiveness).to.equal(undefined);

    user = await User.findById('0');
    user.metrics.numViewsReceivedHourly = new Map();
    for (let i = 24; i > 0; i--) {
      const hourKey = DateTime.utc().minus({ hours: i }).startOf('hour').toISO({ suppressMilliseconds: true });
      if (i % 2 === 0) {
        user.metrics.numViewsReceivedHourly.set(hourKey, 4);
      } else {
        user.metrics.numViewsReceivedHourly.set(hourKey, 3);
      }
    }
    await user.save();

    // start boost with user 0
    res = await request(app)
      .put('/v1/coins/boost')
      .set('authorization', 0)
      .send({ price: 200 });
    expect(res.status).to.equal(200);

    // add few views
    for (let uid of [2, 4, 6, 8]) {
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', uid)
        .send({ user: '0' });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: 'fcmToken',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/boosts/activityToken')
      .set('authorization', 0)
      .send({ activityToken: 'activityToken' });
    expect(res.status).to.equal(200);

    // Move time forward to end boost
    clock.tick(60 * 60 * 1000);

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'triggerBoostSuccessPopup');

    user = await User.findById('0');
    expect(await chatLib.calculateBoostEffectiveness(user)).to.be.lessThan(1);

    res = await request(app)
      .post('/v1/worker/notifyBoostEffectiveness');
    expect(res.status).to.equal(200);

    // should not send notification
    sinon.assert.calledOnce(sendStub);

    let socketTriggered = false;
    await Promise.race([
      socketPromise.then(() => { socketTriggered = true; }),
      new Promise(resolve => setTimeout(resolve, 200)),
    ]);
    expect(socketTriggered).to.equal(false);

    // popup pending should not be added in user profile
    user = await User.findById('0');
    expect(user.boostEffectiveness).to.equal(undefined);

    await destroySocket(socket);
    clock.restore();
  });
});

it('test use_short_boost_in_tests config', async () => {
  const clock = sinon.useFakeTimers({
    now: Date.now(),
    toFake: ['Date'],
  });

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  let user = await User.findById(0);
  expect(user.config.use_short_boost_in_tests).to.equal(undefined);
  expect(user.getBoostDurationMinutes()).to.equal(30);
  user.numBoosts = 1;
  await user.save();

  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.boostDurationMinutes).to.equal(30);

  let metrics = await BoostMetric.find({ user: 0 }).sort({ boostExpiration: -1 });
  expect(metrics.length).to.equal(1);
  expect(metrics[0].durationMinutes).to.equal(30);

  clock.tick(31 * 60 * 1000); // advance 31 minutes

  // update user app version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.102' });
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.appVersion).to.equal('1.13.102');
  expect(user.config.use_short_boost_in_tests).to.equal(undefined);
  expect(user.getBoostDurationMinutes()).to.equal(60);
  user.numBoosts = 1;
  await user.save();

  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.boostDurationMinutes).to.equal(60);

  metrics = await BoostMetric.find({ user: 0 }).sort({ boostExpiration: -1 });
  expect(metrics.length).to.equal(2);
  expect(metrics[0].durationMinutes).to.equal(60);

  // assign test config
  user.config.use_short_boost_in_tests = true;
  user.numBoosts = 1;
  await user.save();
  expect(user.getBoostDurationMinutes()).to.equal(10);

  clock.tick(61 * 60 * 1000); // advance 61 minutes

  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  expect(user.boostDurationMinutes).to.equal(10);

  metrics = await BoostMetric.find({ user: 0 }).sort({ boostExpiration: -1 });
  expect(metrics.length).to.equal(3);
  expect(metrics[0].durationMinutes).to.equal(10);

  clock.tick(11 * 60 * 1000); // advance 11 minutes
  expect(DateTime.fromJSDate(user.boostExpiration) <= DateTime.utc()).to.equal(true);

  clock.restore();
});
