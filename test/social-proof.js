const { DateTime } = require('luxon');
const { expect } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const constants = require('../lib/constants');
const basic = require('../lib/basic');
const { app, validImagePath } = require('./common');
const socialProofCacheLib = require('../lib/social-proof-cache')
const SocialQueryCache = require('../models/social-query-cache')
const PurchaseReceipt = require('../models/purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt')
const User = require('../models/user')
const HourlySystemMetric = require('../models/hourly-system-metrics')
const iapHelper = require('./helper/iap');
const BoostMetric = require('../models/boost-metric')

describe('set cache for social proofs', () => {

  before(async () => {
    constants.enforceVerification.restore();
    sinon.stub(constants, 'enforceVerification').returns(true);
    // create male
    let usersPictureId = []
    for (let i = 1; i <= 2; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      user.gender = 'male'
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      usersPictureId[i] = res.body.pictures[0];

      fakeS3.getObject = function (params) {
        const impl = function (resolve, reject) {
          resolve({
            Body: Buffer.from('mock file content'),
            Metadata: { 'mock-metadata': 'value' },
          });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      const payload = {
        img: 'base-64-image-data',
        secure: {
          version: "2.7.0",
          token: "token-data",
          verification: "verification-data",
          signature: "signature-data",
        },
      };

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', i)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');

    }

    //create female
    for (let i = 3; i <= 4; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      // user.verification.status = i % 2 === 0 ? 'verified' : 'unverified';
      user.gender = 'female'
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      usersPictureId[i] = res.body.pictures[0];

      fakeS3.getObject = function (params) {
        const impl = function (resolve, reject) {
          resolve({
            Body: Buffer.from('mock file content'),
            Metadata: { 'mock-metadata': 'value' },
          });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      const payload = {
        img: 'base-64-image-data',
        secure: {
          version: "2.7.0",
          token: "token-data",
          verification: "verification-data",
          signature: "signature-data",
        },
      };

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', i)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');
    }

    //create non-binary
    for (let i = 5; i <= 6; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(i);
      // user.verification.status = i % 2 === 0 ? 'verified' : 'unverified';
      user.gender = 'non-binary'
      await user.save();

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', i)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      usersPictureId[i] = res.body.pictures[0];

      fakeS3.getObject = function (params) {
        const impl = function (resolve, reject) {
          resolve({
            Body: Buffer.from('mock file content'),
            Metadata: { 'mock-metadata': 'value' },
          });
        };
        return {
          promise: () => new Promise(impl),
        };
      };

      const payload = {
        img: 'base-64-image-data',
        secure: {
          version: "2.7.0",
          token: "token-data",
          verification: "verification-data",
          signature: "signature-data",
        },
      };

      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', i)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');
    }

  });

  beforeEach(async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);
  })

  it('set cache for social proofs', async () => {
      await User.updateMany(
        { _id: { $in: [1, 3, 5] } },
        { $set: { 'verification.status': 'pending' } }
      );

      await Promise.all([
        new PurchaseReceipt({ user: '2', transactionId: '1', purchaseDate: Date.now() }).save(),
        new SuperLikePurchaseReceipt({ user: '4', transactionId: '1', purchaseDate: Date.now() }).save(),
        new CoinPurchaseReceipt({ user: '6', transactionId: '1', purchaseDate: Date.now() }).save(),
        new NeuronPurchaseReceipt({ user: '2', transactionId: '1', purchaseDate: Date.now() }).save(),
        new BoostPurchaseReceipt({ user: '4', transactionId: '1', purchaseDate: Date.now() }).save()
      ]);

      let res = await request(app)
        .post('/v1/worker/setCacheImagesSocialProof')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      let cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('cachedImages');
      expect(cachedImages.length).to.equal(1);
      let userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationMaleCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationFemaleCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationNonBinaryCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('superLikeCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('coinPurchaseCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('neuronPurchaseCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('boostPurchaseCachedImages');
      expect(cachedImages.length).to.equal(1);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4]);

      await User.updateMany(
        { _id: { $in: [1, 3, 5] } },
        { $set: { 'verification.status': 'verified' } }
      );

      await Promise.all([
        new PurchaseReceipt({ user: '1', transactionId: '2', purchaseDate: Date.now() }).save(),
        new SuperLikePurchaseReceipt({ user: '3', transactionId: '2', purchaseDate: Date.now() }).save(),
        new CoinPurchaseReceipt({ user: '5', transactionId: '2', purchaseDate: Date.now() }).save(),
        new NeuronPurchaseReceipt({ user: '1', transactionId: '2', purchaseDate: Date.now() }).save(),
        new BoostPurchaseReceipt({ user: '3', transactionId: '2', purchaseDate: Date.now() }).save()
      ]);

      res = await request(app)
        .post('/v1/worker/setCacheImagesSocialProof')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('cachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2, 1]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationMaleCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2, 1]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationFemaleCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('verificationNonBinaryCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6, 5]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('superLikeCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('coinPurchaseCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([6, 5]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('neuronPurchaseCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([2, 1]);

      cachedImages = await socialProofCacheLib.getCachedImagesSocialProof('boostPurchaseCachedImages');
      expect(cachedImages.length).to.equal(2);
      userIdsAsNumbers = cachedImages.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);

      // Checking if routes are working
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.81' });
      expect(res.status).to.equal(200);

      let user = await User.findById(0);
      user.gender = 'male';
      user.preferences.dating = ['female'];
      await user.save();

      res = await request(app)
        .get('/v1/social-proof/verification')
        .set('authorization', 0);
      expect(res.status).to.equal(200);

      console.log('res: ', res.body);

      userIdsAsNumbers = res.body.images.map(img => parseInt(img.split('/')[0], 10));
      expect(userIdsAsNumbers).to.have.members([4, 3]);
    });
})

it('boost social proof success app_907', async () => {

  let clock = sinon.useFakeTimers({
    now: Date.now(),
    toFake: ['Date'],
  });

  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
      .send({
        appVersion: '1.13.101',
      });
    expect(res.status).to.equal(200);
  }

  receipt = iapHelper.getValidAppleReceipt('boosts_10_v1', Date.now());

  res = await request(app)
    .put('/v1/boosts/purchase')
    .set('authorization', 0)
    .send({
      receipt,
      price: 4000,
      currency: 'JPY',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  let boostMetric = await BoostMetric.findOne({ user: 0 });
  expect(boostMetric).to.exist;
  boostMetric.numLikesReceived = 6;
  await boostMetric.save();

  let user = await User.findById(1);
  user.numBoosts = 1
  await user.save();

  clock.tick(18 * 60 * 60 * 1000);

  res = await request(app)
    .put('/v1/boosts/use')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  await BoostMetric.updateOne(
    { user: 1 },
    { $set: { createdAt: Date.now() } }
  );

  boostMetric = await BoostMetric.findOne({ user: 1 });
  expect(boostMetric).to.exist;
  boostMetric.numLikesReceived = 7;
  await boostMetric.save();

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(403);

  user = await User.findById(1);
  user.config['app_907'] = true;
  await user.save();

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 0)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  const pictureId = res.body.pictures[0];

  user = await User.findById(0);
  expect(user.pictures.length).to.equal(1);
  expect(user.pictures[0]).to.equal(pictureId);

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.boostSuccess.length).to.equal(1);
  expect(res.body.boostSuccess[0].image).to.equal(pictureId);
  expect(res.body.boostSuccess[0].numLikes).to.equal(6);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  const pictureId2 = res.body.pictures[0];

  user = await User.findById(1);
  expect(user.pictures.length).to.equal(1);
  expect(user.pictures[0]).to.equal(pictureId2);

  res = await request(app)
    .post('/v1/worker/setCacheImagesSocialProof')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.boostSuccess.length).to.equal(1);
  expect(res.body.boostSuccess[0].image).to.equal(pictureId);
  expect(res.body.boostSuccess[0].numLikes).to.equal(6);

  clock.tick(2 * 60 * 60 * 1000);

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.boostSuccess.length).to.equal(2);
  expect(res.body.boostSuccess[0].image).to.equal(pictureId2);
  expect(res.body.boostSuccess[0].numLikes).to.equal(7);
  expect(res.body.boostSuccess[1].image).to.equal(pictureId);
  expect(res.body.boostSuccess[1].numLikes).to.equal(6);

  clock.tick(6 * 60 * 60 * 1000);

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.boostSuccess.length).to.equal(1);
  expect(res.body.boostSuccess[0].image).to.equal(pictureId2);
  expect(res.body.boostSuccess[0].numLikes).to.equal(7);

  let cachedSuccess = await SocialQueryCache.findOne({ cacheType: 'boostSuccessCachedData' })
  let cachedUpdatedAt = cachedSuccess.updatedAt;

  clock.tick(24 * 60 * 60 * 1000);

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.boostSuccess.length).to.equal(0);

  cachedSuccess = await SocialQueryCache.findOne({ cacheType: 'boostSuccessCachedData' })
  expect(cachedUpdatedAt).to.not.equal(cachedSuccess.updatedAt);
  cachedUpdatedAt = cachedSuccess.updatedAt;

  res = await request(app)
    .get('/v1/social-proof/boost-success')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.boostSuccess.length).to.equal(0);

  cachedSuccess = await SocialQueryCache.findOne({ cacheType: 'boostSuccessCachedData' })
  expect(cachedUpdatedAt).to.eql(cachedSuccess.updatedAt);
  cachedUpdatedAt = cachedSuccess.updatedAt;

  clock.restore();
});

describe('New Connections Metric', () => {
  beforeEach(async () => {
    await HourlySystemMetric.deleteMany({});
  });

  it('should increment and return new connections metric', async () => {
    let clock = sinon.useFakeTimers({
      now: Date.now(),
    });

    let count = await HourlySystemMetric.getNewConnectionsLastNDays('mNewConnections');
    expect(count).to.equal(0);

    await HourlySystemMetric.increment('numNewConnections');
    await HourlySystemMetric.increment('numNewConnections', 3);

    const currentHour = DateTime.utc().set({ minute: 0, second: 0, millisecond: 0 }).toJSDate();

    const metric = await HourlySystemMetric.findOne({ date: currentHour });
    expect(metric).to.exist;
    expect(metric.metrics.numNewConnections).to.equal(4);

    await HourlySystemMetric.increment('numNewConnections', 1111111);
    clock.tick(1 * 24 * 60 * 60 * 1000); // 1 day
    await HourlySystemMetric.increment('numNewConnections', 10);
    clock.tick(1 * 60 * 60 * 1000); // 1 hour
    await HourlySystemMetric.increment('numNewConnections', 11);
    clock.tick(1 * 60 * 60 * 1000); // 1 hour
    await HourlySystemMetric.increment('numNewConnections', 12);
    clock.tick(1 * 60 * 60 * 1000); // 1 hour
    await HourlySystemMetric.increment('numNewConnections', 13);
    clock.tick(6 * 24 * 60 * 60 * 1000); // 6 days
    await HourlySystemMetric.increment('numNewConnections', 14);

    count = await HourlySystemMetric.getNewConnectionsLastNDays('numNewConnections');
    expect(count).to.equal(60);

    const totalMetricCreated = await HourlySystemMetric.countDocuments();
    expect(totalMetricCreated).to.equal(6);

    clock.restore();
  });
  it('if below cutoff date, should return 403', async () => {
    let clock = sinon.useFakeTimers({
      now: new Date('2025-10-11'),
    });

    await HourlySystemMetric.increment('numNewConnections', 10);

    // init app for user 100
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 100);
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: 100 });
    user.config.app_887 = true;
    await user.save();

    res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 100);
    expect(res.status).to.equal(403);

    clock.tick(7 * 24 * 60 * 60 * 1000); // 7 days

    res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 100);
    expect(res.status).to.equal(200);
    expect(res.body.newConnectionsCount).to.equal(10);

    clock.restore();
  });
});

describe('New Connections social proof', () => {
  beforeEach(async () => {
    await HourlySystemMetric.deleteMany({});
  });

  it('should increment new connections metric when match is created', async () => {
    let clock = sinon.useFakeTimers({
      now: new Date('2025-10-20'),
    });

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 100);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 101);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 100)
      .send({
        user: '101',
      });
    expect(res.status).to.equal(200);

    let count = await HourlySystemMetric.getNewConnectionsLastNDays('numNewConnections');
    expect(count).to.equal(0);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 101)
      .send({
        user: '100',
      });
    expect(res.status).to.equal(200);

    count = await HourlySystemMetric.getNewConnectionsLastNDays('numNewConnections');
    expect(count).to.equal(1);

    const currentHour = DateTime.utc().set({ minute: 0, second: 0, millisecond: 0 }).toJSDate();
    const metric = await HourlySystemMetric.findOne({ date: currentHour });
    expect(metric).to.exist;
    expect(metric.metrics.numNewConnections).to.equal(1);

    clock.tick(1 * 60 * 60 * 1000);

    count = await HourlySystemMetric.getNewConnectionsLastNDays('numNewConnections');
    expect(count).to.equal(1);

    res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 100);
    expect(res.status).to.equal(403);

    let user = await User.findOne({ _id: 100 });
    user.config.app_887 = true;
    await user.save();

    res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 100);
    expect(res.status).to.equal(200);
    expect(res.body.newConnectionsCount).to.equal(1);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 104);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 104)
      .send({
        user: '100',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 100)
      .send({
        user: '104',
      });
    expect(res.status).to.equal(200);

    count = await HourlySystemMetric.getNewConnectionsLastNDays('numNewConnections');
    expect(count).to.equal(2);

    res = await request(app)
      .get('/v1/social-proof/new-connections')
      .set('authorization', 100);
    expect(res.status).to.equal(200);
    expect(res.body.newConnectionsCount).to.equal(2);

    const totalMetricCreated = await HourlySystemMetric.countDocuments();
    expect(totalMetricCreated).to.equal(2);

    clock.restore();
  });
});
