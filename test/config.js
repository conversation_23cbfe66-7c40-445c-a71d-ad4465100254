const { expect } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const moment = require('moment');
const { app, mongoose, validImagePath } = require('./common');
const User = require('../models/user');
const basic = require('../lib/basic');
const constants = require('../lib/constants');
const chatLib = require('../lib/chat');
const pricingExperimentLib = require('../lib/pricing-experiment');
const onboardingVideoConfigLib = require('../lib/onboarding-video-config');
const openai = require('../lib/openai');
const iapHelper = require('./helper/iap');
const BoostPurchaseReceipt = require('../models/boost-purchase-receipt');
const BoostMetric = require("../models/boost-metric");
const Interest = require('../models/interest')
const ct = require('countries-and-timezones');
const { DateTime } = require('luxon');
const { notifs, reset, waitFor } = require('./stub');
const geoip = require('geoip-lite');

describe('config', () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
  });

  it('finalized configs', async () => {
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_765).to.equal(null);
    expect(res.body.popular_languages).to.include('es');
    expect(res.body.allow_automated_chat).to.equal(false);
    expect(res.body.show_segmented_sign_up).to.equal(true);
    expect(res.body.show_skip_buttons).to.equal(true);
    expect(res.body.show_continue_sign_up_option).to.equal(false);
    expect(res.body.show_pricing_variant).to.equal(false);
    expect(res.body.show_get_more_button).to.equal(null);
    expect(res.body.show_orb).to.equal(false);
    expect(res.body.change_boo_infinity_value_prop_text).to.equal(1);
    expect(res.body.change_boo_infinity_value_prop).to.equal(4);
    expect(res.body.change_send_dm_text).to.equal(false);
    expect(res.body.more_recs_for_higher_levels).to.equal(true);
    expect(res.body.show_boo_infinity_price_per_day).to.equal(false);
    expect(res.body.show_boo_infinity_promise).to.equal(true);
    expect(res.body.show_interest_num_followers).to.equal(true);
    expect(res.body.show_100_coins_option).to.equal(true);
    expect(res.body.show_post_icon_on_bottom).to.equal(false);
    expect(res.body.show_telepathy_in_profile).to.equal(0);
    expect(res.body.add_double_tap_to_purchase_option).to.equal(false);
    expect(res.body.free_telepathy).to.equal(true);
    expect(res.body.change_flash_sale_time_limit).to.equal(1);
    expect(res.body.add_haptic_feedback_to_purchasing).to.equal(true);
    expect(res.body.change_boo_infinity_save_text).to.equal(false);
    expect(res.body.change_autoplay_timing_social_proof).to.equal(false);
    expect(res.body.glow_middle_option).to.equal(false);
    expect(res.body.show_match_stats_during_signup).to.equal(true);
    expect(res.body.show_progress_bar_on_edit).to.equal(true);
    expect(res.body.change_home_screen_to_social).to.equal(false);
    expect(res.body.show_photo_tips).to.equal(true);
    expect(res.body.show_mutual_interests_on_new_chat).to.equal(true);
    expect(res.body.move_prompts_to_top_of_edit).to.equal(true);
    expect(res.body.change_daily_limit_reached_popup).to.equal(true);
    expect(res.body.show_expired_likes).to.equal(true);
    expect(res.body.move_description_up_in_edit).to.equal(true);
    expect(res.body.use_boo_infinity_gifs).to.equal(false);
    expect(res.body.use_summoning_gif).to.equal(true);
    expect(res.body.show_post_to_social_in_signup).to.equal(true);
    expect(res.body.change_summoning_gif).to.equal(true);
    expect(res.body.show_view_count_on_posts).to.equal(false);
    expect(res.body.use_boo_infinity_gifs_round_2).to.equal(true);
    expect(res.body.show_local_time).to.equal(true);
    expect(res.body.show_verify_profile_popup).to.equal(true);
    expect(res.body.change_love_description).to.equal(false);
    expect(res.body.change_save_text).to.equal(true);
    expect(res.body.remove_summoning_gif).to.equal(true);
    expect(res.body.change_verification_picture).to.equal(false);
    expect(res.body.show_description_tips).to.equal(true);
    expect(res.body.show_social_tooltip_after_daily_limit).to.equal(false);
    expect(res.body.show_social_tooltip_tutorial).to.equal(true);
    expect(res.body.get_all_contacts).to.equal(true);
    expect(res.body.change_save_text_v2).to.equal(true);
    expect(res.body.move_value_page_first).to.equal(false);
    expect(res.body.show_related_posts).to.equal(true);
    expect(res.body.move_most_popular_to_3).to.equal(true);
    expect(res.body.non_english_sort_best_week).to.equal(false);
    expect(res.body.non_english_sort_best_week_v2).to.equal(false);
    expect(res.body.show_12_sold_out).to.equal(true);
    expect(res.body.make_12_purchasable).to.equal(false);
    expect(res.body.show_similar_interests_v2).to.equal(true);
    expect(res.body.move_post_button).to.equal(false);
    expect(res.body.remove_soulmate_slide).to.equal(false);
    expect(res.body.show_activate_boo_infinity_quest).to.equal(true);
    expect(res.body.move_delete_testimonials).to.equal(false);
    expect(res.body.show_similar_interests_v3).to.equal(false);
    expect(res.body.change_boo_infinity_order).to.equal(false);
    expect(res.body.ads_for_coins).to.equal(true);
    expect(res.body.social_ads).to.equal(true);
    expect(res.body.show_invite_quest).to.equal(true);
    expect(res.body.social_ad_frequency_index).to.equal(10);
    expect(res.body.change_mbti_ghosts).to.equal(false);
    expect(res.body.show_multiple_ads).to.equal(true);
    expect(res.body.change_referral_text).to.equal(false);
    expect(res.body.remove_coins_from_premium).to.equal(true);
    expect(res.body.show_action_items).to.equal(false);
    expect(res.body.default_following).to.equal(false);
    expect(res.body.move_telepathy).to.equal(2);
    expect(res.body.change_mbti_ghosts_v2).to.equal(false);
    expect(res.body.show_boost_post_powerup).to.equal(true);
    expect(res.body.go_to_home_after_quiz).to.equal(false);
    expect(res.body.go_to_social_after_signup).to.equal(false);
    expect(res.body.require_notifications_for_swiping).to.equal(false);
    expect(res.body.use_ghost_verification_pose).to.equal(false);
    expect(res.body.show_advanced_filters).to.equal(false);
    expect(res.body.add_social_to_delete).to.equal(true);
    expect(res.body.show_view_last_seen_powerup).to.equal(true);
    expect(res.body.allow_sticker_pack_coin_purchase).to.equal(true);
    expect(res.body.change_dimension_icon).to.equal(false);
    expect(res.body.make_premium_awards).to.equal(false);
    expect(res.body.change_soulmate_slide).to.equal(false);
    expect(res.body.change_delete_button_color).to.equal(true);
    expect(res.body.change_boo_infinity_subtitle).to.equal(false);
    expect(res.body.show_6_sold_out).to.equal(false);
    expect(res.body.show_bottom_icon_text).to.equal(true);
    expect(res.body.show_waving_boo_infinity_animation).to.equal(false);
    expect(res.body.change_activate_to_continue).to.equal(true);
    expect(res.body.show_premium_popup_on_app_open_v1).to.equal(false);
    expect(res.body.change_mbti_ghosts_v3).to.equal(true);
    expect(res.body.change_activate_to_continue_on_profile).to.equal(false);
    expect(res.body.make_translation_premium).to.equal(false);
    expect(res.body.new_karma_system).to.equal(false);
    expect(res.body.change_verification_pose).to.equal(true);
    expect(res.body.show_flash_sale_on_every_open).to.equal(true);
    expect(res.body.add_side_bar).to.equal(true);
    expect(res.body.show_flash_sale_timer).to.equal(true);
    expect(res.body.remove_profile_from_bottom_add_create).to.equal(true);
    expect(res.body.add_sale_banner).to.equal(true);
    expect(res.body.remove_12_sold_out).to.equal(true);
    expect(res.body.coin_rewards_for_social).to.equal(false);
    expect(res.body.first_message_karma_award).to.equal(true);
    expect(res.body.boo_infinity_purchase_animation).to.equal(false);
    expect(res.body.liveness_verification).to.equal(true);
    expect(res.body.show_new_match_tutorial).to.equal(true);
    expect(res.body.use_bunny_cdn).to.equal(true);
    expect(res.body.rate_app_after_15).to.equal(true);
    expect(res.body.show_ads_for_swipes).to.equal(false);
    expect(res.body.pulsing_glow).to.equal(false);
    expect(res.body.show_photo_toast).to.equal(false);
    expect(res.body.sign_up_interests_mandatory).to.equal(true);
    expect(res.body.social_tutorial).to.equal(true);
    expect(res.body.verify_to_message).to.equal(true);
    expect(res.body.rive_boo_infinity_animations).to.equal(true);
    expect(res.body.lifestyle_in_signup).to.equal(true);
    expect(res.body.improve_delete_account).to.equal(true);
    expect(res.body.show_coins_social_proof).to.equal(true);
    expect(res.body.see_who_viewed_profile).to.equal(true);
    expect(res.body.for_you_algo).to.equal(false);
    expect(res.body.boo_infinity_full_screen_popup).to.equal(true);
    expect(res.body.coins_flash_sale).to.equal(true);
    expect(res.body.super_love_design_consistency).to.equal(false);
    expect(res.body.new_coins_animation).to.equal(true);
    expect(res.body.lava_rate_app).to.equal(true);
    expect(res.body.change_leveled_up_number_format).to.equal(true);
    expect(res.body.coins_design_consistency).to.equal(true);
    expect(res.body.exclude_3_super_love_flash_sale).to.equal(true);
    expect(res.body.full_screen_coins).to.equal(true);
    expect(res.body.full_screen_super_love).to.equal(true);
    expect(res.body.refund_disclaimer).to.equal(false);
    expect(res.body.boo_infinity_shimmer_prices).to.equal(false);
    expect(res.body.slow_signup_animations).to.equal(true);
    expect(res.body.use_number_formatting).to.equal(true);
    expect(res.body.verify_to_post_comment).to.equal(true);
    expect(res.body.immediate_account_deletion).to.equal(false);
    expect(res.body.super_love_design_consistency_v2).to.equal(false);
    expect(res.body.exclude_1000_coins_from_sale).to.equal(true);
    expect(res.body.larger_coins_animations).to.equal(false);
    expect(res.body.super_love_sale_on_click).to.equal(true);
    expect(res.body.show_onboarding_video).to.equal(true);
    expect(res.body.show_network_debug_button).to.equal(true);
    expect(res.body.god_mode).to.equal(false);
    expect(res.body.use_memory_image).to.equal();
    expect(res.body.change_super_love_animation).to.equal(true);
    expect(res.body.profile_super_love).to.equal(true);
    expect(res.body.hide_nested_comments).to.equal(2);
    expect(res.body.user_picture_in_super_love_purchase).to.equal(false);
    expect(res.body.infp_soulmate_slide).to.equal(false);
    expect(res.body.quotes_in_delete_account).to.equal(true);
    expect(res.body.boo_infinity_sale_animation).to.equal(false);
    expect(res.body.coins_sale_animation).to.equal(true);
    expect(res.body.super_love_sale_animation).to.equal(false);
    expect(res.body.remove_love_count).to.equal(false);
    expect(res.body.friend_request_notification).to.equal(true);
    expect(res.body.remove_for_you_for_non_popular_languages).to.equal(true);
    expect(res.body.social_premium).to.equal(false);
    expect(res.body.show_boo_ai).to.equal(true);
    expect(res.body.english_faq).to.equal(true);
    expect(res.body.tailored_super_love_gender_animation).to.equal(true);
    expect(res.body.vampire_werewolf_soulmate).to.equal(false);
    expect(res.body.show_boo_ai_social).to.equal(true);
    expect(res.body.show_boo_ai_tutorial_first_time).to.equal(true);
    expect(res.body.gendered_boo_infinity_animations).to.equal(true);
    expect(res.body.ai_icebreaker_tutorial).to.equal(true);
    expect(res.body.show_interests_and_verified_signup_carousel).to.equal(true);
    expect(res.body.change_boo_ai_purchase_animation).to.equal(true);
    expect(res.body.boost_on_requests_page).to.equal(false);
    expect(res.body.show_purchase_super_love).to.equal(true);
    expect(res.body.see_who_viewed_in_requests).to.equal(true);
    expect(res.body.read_receipts_in_new_match).to.equal(false);
    expect(res.body.purchases_in_sidebar).to.equal(false);
    expect(res.body.use_aws_images_v2).to.equal(true);
    expect(res.body.app_55).to.equal(true);
    expect(res.body.app_71).to.equal(false);
    expect(res.body.app_53).to.equal(true);
    expect(res.body.app_54).to.equal(true);
    expect(res.body.app_105).to.equal(true);
    expect(res.body.app_108).to.equal(false);
    expect(res.body.app_68).to.equal(false);
    expect(res.body.app_83).to.equal(false);
    expect(res.body.app_69).to.equal(false);
    expect(res.body.app_41).to.equal(false);
    expect(res.body.app_112).to.equal(false);
    expect(res.body.app_95).to.equal(false);
    expect(res.body.app_39).to.equal(false);
    expect(res.body.app_44).to.equal(true);
    expect(res.body.app_49).to.equal(true);
    expect(res.body.app_122).to.equal(false);
    expect(res.body.app_40).to.equal(false);
    expect(res.body.app_98).to.equal(true);
    expect(res.body.app_126).to.equal(true);
    expect(res.body.app_52).to.equal(false);
    expect(res.body.app_132).to.equal(false);
    expect(res.body.app_149).to.equal(false);
    expect(res.body.app_151).to.equal(false);
    expect(res.body.app_138).to.equal(false);
    expect(res.body.app_152).to.equal(false);
    expect(res.body.app_123).to.equal(true);
    expect(res.body.app_131).to.equal(true);
    expect(res.body.app_159).to.equal(true);
    expect(res.body.app_150).to.equal(false);
    expect(res.body.app_107).to.equal(true);
    expect(res.body.app_164).to.equal(true);
    expect(res.body.app_125).to.equal(true);
    expect(res.body.app_114).to.equal(true);
    expect(res.body.app_120).to.equal(false);
    expect(res.body.app_213).to.equal(true);
    expect(res.body.app_163).to.equal(true);
    expect(res.body.app_194).to.equal(true);
    expect(res.body.app_178).to.equal(true);
    expect(res.body.app_142).to.equal(false);
    expect(res.body.app_175).to.equal(true);
    expect(res.body.post_visibility_score).to.equal(false);
    expect(res.body.app_190).to.equal(true);
    expect(res.body.app_153).to.equal(true);
    expect(res.body.app_174).to.equal(false);
    expect(res.body.app_168).to.equal(false);
    expect(res.body.app_171).to.equal(false);
    expect(res.body.app_201).to.equal(false);
    expect(res.body.app_197).to.equal(false);
    expect(res.body.app_146).to.equal(true);
    expect(res.body.app_146_v2).to.equal(true);
    expect(res.body.app_226).to.equal(false);
    expect(res.body.app_220).to.equal(false);
    expect(res.body.app_221).to.equal(true);
    expect(res.body.app_188).to.equal(true);
    expect(res.body.app_234).to.equal(true);
    expect(res.body.app_240).to.equal(false);
    expect(res.body.app_165).to.equal(false);
    expect(res.body.app_274).to.equal(false);
    expect(res.body.app_287).to.equal(true);
    expect(res.body.app_304).to.equal(true);
    expect(res.body.app_268).to.equal(true);
    expect(res.body.app_191).to.equal(true);
    expect(res.body.app_312).to.equal(false);
    expect(res.body.app_291).to.equal(true);
    expect(res.body.app_185).to.equal(false);
    expect(res.body.app_281).to.equal(false);
    expect(res.body.app_300).to.equal(true);
    expect(res.body.app_311).to.equal(false);
    expect(res.body.app_288).to.equal(true);
    expect(res.body.app_70).to.equal(false);
    expect(res.body.app_295).to.equal(true);
    expect(res.body.app_297).to.equal(true);
    expect(res.body.app_89).to.equal(true);
    expect(res.body.app_296).to.equal(false);
    expect(res.body.app_310).to.equal(true);
    expect(res.body.app_335).to.equal(true);
    expect(res.body.app_316).to.equal(true);
    expect(res.body.app_302).to.equal(false);
    expect(res.body.app_323).to.equal(false);
    expect(res.body.app_301).to.equal(false);
    expect(res.body.app_322).to.equal(true);
    expect(res.body.app_334).to.equal(false);
    expect(res.body.app_241).to.equal(true);
    expect(res.body.app_241_v2).to.equal(true);
    expect(res.body.app_351).to.equal(true);
    expect(res.body.app_325).to.equal(true);
    expect(res.body.app_378).to.equal(false);
    expect(res.body.app_236).to.equal(false);
    expect(res.body.app_376).to.equal(false);
    expect(res.body.app_355).to.equal(false);
    expect(res.body.app_381).to.equal(false);
    expect(res.body.app_309).to.equal(true);
    expect(res.body.app_365).to.equal(true);
    expect(res.body.app_319).to.equal(false);
    expect(res.body.app_237).to.equal(false);
    expect(res.body.app_367).to.equal(false);
    expect(res.body.app_421).to.equal(true);
    expect(res.body.app_321).to.equal(false);
    expect(res.body.app_414).to.equal(false);
    expect(res.body.app_406).to.equal(false);
    expect(res.body.app_418).to.equal(true);
    expect(res.body.app_416).to.equal(false);
    expect(res.body.app_374).to.equal(true);
    expect(res.body.app_366).to.equal(false);
    expect(res.body.app_371).to.equal(true);
    expect(res.body.show_distance_filter).to.equal(true);
    expect(res.body.app_441).to.equal(false);
    expect(res.body.app_415).to.equal(false);
    expect(res.body.app_405).to.equal(false);
    expect(res.body.app_326).to.equal(false);
    expect(res.body.app_315).to.equal(true);
    expect(res.body.app_436).to.equal(true);
    expect(res.body.app_340).to.equal(true);
    expect(res.body.app_442).to.equal(false);
    expect(res.body.app_420).to.equal(true);
    expect(res.body.app_383).to.equal(true);
    expect(res.body.app_439).to.equal(false);
    expect(res.body.app_486).to.equal(true);
    expect(res.body.app_390).to.equal(true);
    expect(res.body.app_437).to.equal(true);
    expect(res.body.app_182).to.equal(true);
    expect(res.body.app_422).to.equal(true);
    expect(res.body.app_438).to.equal(false);
    expect(res.body.app_473).to.equal(false);
    expect(res.body.app_460).to.equal(false);
    expect(res.body.app_482).to.equal(true);
    expect(res.body.app_493).to.equal(false);
    expect(res.body.app_474).to.equal(false);
    expect(res.body.app_497).to.equal(false);
    expect(res.body.app_116).to.equal(false);
    expect(res.body.app_472).to.equal(true);
    expect(res.body.app_506).to.equal(true);
    expect(res.body.app_410).to.equal(true);
    expect(res.body.app_478).to.equal(false);
    expect(res.body.app_459).to.equal(true);
    expect(res.body.app_496).to.equal(false);
    expect(res.body.app_483).to.equal(true);
    expect(res.body.app_498).to.equal(true);
    expect(res.body.app_431_th).to.equal(false);
    expect(res.body.app_431_vi).to.equal(false);
    expect(res.body.app_431_ar).to.equal(false);
    expect(res.body.app_431_ja).to.equal(false);
    expect(res.body.app_431_ko).to.equal(false);
    expect(res.body.app_431_ms).to.equal(false);
    expect(res.body.app_510).to.equal(true);
    expect(res.body.app_495).to.equal(false);
    expect(res.body.app_514).to.equal(false);
    expect(res.body.app_518).to.equal(false);
    expect(res.body.app_463).to.equal(true);
    expect(res.body.app_463_v2).to.equal(true);
    expect(res.body.app_576).to.equal(true);
    expect(res.body.app_508).to.equal(false);
    expect(res.body.app_564).to.equal(true);
    expect(res.body.app_566).to.equal(true);
    expect(res.body.app_566_v2).to.equal(true);
    expect(res.body.app_544).to.equal(true);
    expect(res.body.app_511).to.equal(false);
    expect(res.body.app_440).to.equal(false);
    expect(res.body.app_454).to.equal(true);
    expect(res.body.app_580).to.equal(false);
    expect(res.body.app_465).to.equal(true);
    expect(res.body.app_141).to.equal(true);
    expect(res.body.app_504).to.equal(true);
    expect(res.body.app_555).to.equal(false);
    expect(res.body.app_570).to.equal(true);
    expect(res.body.app_571).to.equal(false);
    expect(res.body.app_536).to.equal(true);
    expect(res.body.app_605).to.equal(true);
    expect(res.body.app_388).to.equal(true);
    expect(res.body.app_339).to.equal(false);
    expect(res.body.app_574).to.equal(false);
    expect(res.body.app_427).to.equal(false);
    expect(res.body.app_657).to.equal(false);
    expect(res.body.app_647).to.equal(false);
    expect(res.body.app_327).to.equal(false);
    expect(res.body.app_299).to.equal(true);
    expect(res.body.app_362).to.equal(false);
    expect(res.body.app_447).to.equal(true);
    expect(res.body.app_481).to.equal(false);
    expect(res.body.app_543).to.equal(true);
    expect(res.body.app_599).to.equal(true);
    expect(res.body.app_696).to.equal(false);
    expect(res.body.app_623).to.equal(true);
    expect(res.body.app_631).to.equal(false);
    expect(res.body.app_654).to.equal(true);
    expect(res.body.app_87).to.equal(true);
    expect(res.body.app_709).to.equal(true);
    expect(res.body.app_661).to.equal(true);
    expect(res.body.app_688).to.equal(true);
    expect(res.body.app_689).to.equal(false);
    expect(res.body.app_677).to.equal(false);
    expect(res.body.app_665).to.equal(false);
    expect(res.body.app_708).to.equal(true);
    expect(res.body.app_736).to.equal(true);
    expect(res.body.app_716).to.equal(false);
    expect(res.body.app_730).to.equal(false);
    expect(res.body.app_674).to.equal(false);
    expect(res.body.app_99).to.equal(false);
    expect(res.body.app_750).to.equal(true);
    expect(res.body.app_735).to.equal(false);
    expect(res.body.app_648).to.equal(true);
    expect(res.body.app_717).to.equal(false);
    expect(res.body.app_763).to.equal(false);
    expect(res.body.app_767).to.equal(true);
    expect(res.body.app_795).to.equal(true);
    expect(res.body.app_760).to.equal(true);
    expect(res.body.app_517).to.equal(true);
    expect(res.body.app_162).to.equal(false);
    expect(res.body.app_813).to.equal(false);
    expect(res.body.app_788).to.equal(true);
    expect(res.body.app_797).to.equal(true);
    expect(res.body.app_204).to.equal(false);
    expect(res.body.app_799).to.equal(false);
    expect(res.body.app_782).to.equal(false);
    expect(res.body.app_733).to.equal(true);
    expect(res.body.app_353).to.equal(false);
    expect(res.body.app_800).to.equal(false);
    expect(res.body.app_821).to.equal(false);
    expect(res.body.app_793).to.equal(true);
    expect(res.body.app_832).to.equal(true);
    expect(res.body.app_839).to.equal(false);
    expect(res.body.app_780).to.equal(true);
    expect(res.body.app_802).to.equal(true);
    expect(res.body.app_833).to.equal(false);
    expect(res.body.app_794).to.equal(false);
    expect(res.body.app_835).to.equal(false);
    expect(res.body.app_837).to.equal(true);
    expect(res.body.app_841).to.equal(true);
    expect(res.body.app_859).to.equal(false);
    expect(res.body.app_849).to.equal(false);
    expect(res.body.app_825).to.equal(true);
    expect(res.body.app_855).to.equal(true);
    expect(res.body.app_857).to.equal(false);
    expect(res.body.app_840).to.equal(true);
    expect(res.body.app_749).to.equal(true);
    expect(res.body.app_834).to.equal(true);
    expect(res.body.app_848).to.equal(true);
    expect(res.body.app_850).to.equal(true);
    expect(res.body.app_851).to.equal(false);
    expect(res.body.app_853).to.equal(false);
    expect(res.body.app_854).to.equal(false);
    expect(res.body.app_867).to.equal(true);
    expect(res.body.app_880).to.equal(true);
    expect(res.body.app_873).to.equal(false);
    expect(res.body.app_852).to.equal(false);
    expect(res.body.app_856).to.equal(true);
    expect(res.body.app_858).to.equal(true);
    expect(res.body.app_877).to.equal(true);
    expect(res.body.app_798).to.equal(false);
    expect(res.body.app_860).to.equal(false);
    expect(res.body.app_879).to.equal(0);
    expect(res.body.app_875).to.equal(true);
    expect(res.body.app_827).to.equal();
    expect(res.body.app_829).to.equal(false);
    expect(res.body.app_870).to.equal(true);
    expect(res.body.app_882).to.equal(true);
    expect(res.body.app_886).to.equal(false);
    expect(res.body.app_892).to.equal(true);
    expect(res.body.app_888).to.equal(false);
    expect(res.body.app_876_v3).to.equal(false);
    expect(res.body.app_914).to.equal(false);
    expect(res.body.app_920).to.equal(false);
    expect(res.body.app_911).to.equal(false);
    expect(res.body.app_919).to.equal(false);
    expect(res.body.app_921).to.equal(false);
    expect(res.body.app_785).to.equal(false);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    await mongoose.connection.db.dropDatabase();

    // os-specific configs
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'android' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_827).to.equal(true);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
  });

  it('experimental configs', async () => {
    const configs = [
    ];
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    for (const config of configs) {
      expect(typeof res.body[config]).to.equal('boolean');
    }
    const saved = res.body;
    console.log(saved);

    // should not change
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    for (const config of configs) {
      expect(res.body[config]).to.equal(saved[config]);
    }
  });

  it('lower price for younger users', async () => {
    let user;

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.pricing_variant_number).to.equal(null);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.pricing_variant_number).to.equal(1);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_6_months_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.pricing_variant_number).to.equal(null);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
  });

  it('lower price + flash sale for younger users', async () => {
    let user;

    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.pricing_variant_number).to.equal(null);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.pricing_variant_number).to.equal(1);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.pricing_variant_number).to.equal(null);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
  });

  it('app_350 - true for premium users only', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_350).to.equal(false);

    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_350).to.equal(true);
  });

  it('app_384', async () => {
    // assigning app_384 only to zh-Hans and zh-Hant
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.58', locale: 'en' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_384).to.equal();

    await mongoose.connection.db.dropDatabase();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.58', locale: 'zh-Hans' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_384).to.equal();

    await mongoose.connection.db.dropDatabase();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.58', locale: 'zh-Hant' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.app_384).to.equal();
  });

  it('free telepathy', async () => {
    // user must have mbti to view telepathy
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 0)
      .send({
        mbti: 'ESTJ',
      });
    expect(res.status).to.equal(200);

    // get telepathy - categories are unlocked
    res = await request(app)
      .get('/v1/personality/allTelepathyDescriptions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ISTJ.categories[res.body.ISTJ.categories.length - 1].locked).to.equal(false);
  });

  it('flash sale product ids', async () => {
    // no app version - no flash sale
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
  });

  it('no flash sale for 1 month subscription', async () => {
    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.25' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    // young user
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);
  });

  it('1 3 6 12 month subscriptions with cheaper 6 month', async () => {
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    // old version should only get 3 product ids
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    // update version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.31' });
    expect(res.status).to.equal(200);

    // no flash sale
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
      'boo_infinity_12_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_12_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_6_months_variant_1',
      'boo_infinity_12_months_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_12_months_discount_50_variant_1',
    ]);

    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.31' });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_12_months_discount_50',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_12_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_12_months_discount_50_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_12_months_discount_50_variant_1',
    ]);
  });

  it('12 6 3 1 month subscriptions', async () => {
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    // old version should only get 3 product ids
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    // update version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.33' });
    expect(res.status).to.equal(200);

    // no flash sale
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months',
      'boo_infinity_6_months',
      'boo_infinity_3_months',
      'boo_infinity_1_month',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_variant_1',
      'boo_infinity_6_months_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_1_month_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
    ]);

    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.33' });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_1_month_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
    ]);
  });

  /*
  it('alternate subscriptions', async function() {

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.subscription_products.boo_infinity).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_12_months',
    ]);
    expect(res.body.subscription_products.unlimited_likes).to.eql();
    expect(res.body.subscription_products.unlimited_dms).to.eql();

    user = await User.findById(0);
    user.config.show_alternate_subscriptions = 1;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.subscription_products.unlimited_likes).to.eql(['unlimited_likes_1_month']);
    expect(res.body.subscription_products.unlimited_dms).to.eql(['unlimited_dms_1_month']);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear()-20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.subscription_products.unlimited_likes).to.eql(['unlimited_likes_1_month_variant_1']);
    expect(res.body.subscription_products.unlimited_dms).to.eql(['unlimited_dms_1_month_variant_1']);
  });
  */

  it('lifetime subscription 6/3/1/lifetime', async () => {
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    // old version should only get 3 product ids
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    // update version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.36' });
    expect(res.status).to.equal(200);

    // no flash sale
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_6_months',
      'boo_infinity_3_months',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_6_months_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
    ]);

    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.36' });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
    ]);
  });

  it('lifetime subscription 12/6/3/1/lifetime', async () => {
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    // old version should only get 3 product ids
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    // update version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.48' });
    expect(res.status).to.equal(200);

    // no flash sale
    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months',
      'boo_infinity_6_months',
      'boo_infinity_3_months',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_variant_1',
      'boo_infinity_6_months_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
    ]);

    // flash sale
    await User.deleteMany();
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.48' });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 30, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
    ]);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_sale_active).to.equal(true);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
    ]);
  });

  it('new pricing for dating only and friends only', async () => {
    user = await User.findById(0);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month',
      'boo_infinity_3_months',
      'boo_infinity_6_months',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
    ]);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_6_months_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant',
      'boo_infinity_3_months_variant',
      'boo_infinity_6_months_variant',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant',
      'boo_infinity_6_months_discount_50_variant',
    ]);

    // not applicable for young users
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_variant_1',
      'boo_infinity_6_months_variant_1',
    ]);
    expect(res.body.infinity_sale_active).to.equal(false);
    expect(res.body.product_ids_with_sale).to.eql([
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
    ]);
  });

  it('coins flash sale', async () => {
    // no sale
    res = await request(app)
      .get('/v1/config/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins_product_ids).to.eql([
      '10000_coins',
      '4000_coins',
      '1000_coins',
      '100_coins',
    ]);
    expect(res.body.coins_discounted_product_ids).to.equal(null);
    expect(res.body.coins_discount).to.equal(null);
    expect(res.body.coins_sale_end_date).to.equal(null);

    // enable sale
    user = await User.findById(0);
    user.coinsFlashSaleEndDate = moment().add(1, 'days');
    await user.save();

    res = await request(app)
      .get('/v1/config/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.coins_product_ids).to.eql([
      '10000_coins',
      '4000_coins',
      '1000_coins',
      '100_coins',
    ]);
    expect(res.body.coins_discounted_product_ids).to.eql([
      '10000_coins_discount_30',
      '4000_coins_discount_30',
      '1000_coins_discount_30',
      '100_coins_discount_30',
    ]);
    expect(res.body.coins_discount).to.equal(30);
    expect(res.body.coins_sale_end_date).to.not.equal();
  });

});

describe('show device language feed', async () => {
  it('eligible', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.11.45',
        deviceLanguage: 'es',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.show_device_language_feed).to.equal(true);
  });

  it('ineligible language', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.11.45',
        deviceLanguage: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.show_device_language_feed).to.equal(false);
  });

  it('default_user_to_device_language_feed true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.11.66',
        deviceLanguage: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.show_device_language_feed).to.equal(true);
  });

  it('default_user_to_device_language_feed false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.11.66',
        deviceLanguage: 'haw',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.show_device_language_feed).to.equal(false);
  });
});

describe('country pricing experiments', async () => {
  beforeEach(async () => {
    sinon.stub(pricingExperimentLib, 'getGoogleCountryData').callsFake((countryName) => {
      const countryData = {
        Canada: {
          college: {
            baseline: 1,
            variant: null,
          },
          dating: {
            baseline: 1,
            variant: null,
          },
          friends: {
            baseline: 1,
            variant: null,
          },
          both: {
            baseline: 1,
            variant: null,
          },
        },
        Indonesia: {
          college: {
            baseline: 1,
            variant: 3,
          },
          dating: {
            baseline: 1,
            variant: 3,
          },
          friends: {
            baseline: 1,
            variant: 4,
          },
          both: {
            baseline: 1,
            variant: 3,
          },
        },
        'United Kingdom': {
          college: {
            baseline: 3,
            variant: null,
          },
          dating: {
            baseline: 3,
            variant: null,
          },
          friends: {
            baseline: 3,
            variant: null,
          },
          both: {
            baseline: 3,
            variant: null,
          },
        },
        'United States': {
          college: {
            baseline: 3,
            variant: 4,
          },
          dating: {
            baseline: 3,
            variant: 4,
          },
          friends: {
            baseline: 3,
            variant: 4,
          },
          both: {
            baseline: 3,
            variant: 4,
          },
        },
        Vietnam: {
          college: {
            baseline: 1,
            variant: null,
          },
          dating: {
            baseline: 1,
            variant: 3,
          },
          friends: {
            baseline: 3,
            variant: 4,
          },
          both: {
            baseline: 3,
            variant: null,
          },
        },
        Iceland: {
          college: {
            baseline: 1,
            variant: 6,
          },
          dating: {
            baseline: 1,
            variant: 6,
          },
          friends: {
            baseline: 1,
            variant: 6,
          },
          both: {
            baseline: 1,
            variant: 6,
          },
        },
      };
      return countryData[countryName];
    });
  });

  it('config assigned on putting preferences', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.infinity_price_v3).to.equal(true);
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_m3_v3_d50',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);

    // changing preferences doesn't affect config
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.infinity_price_v3).to.equal(true);
    expect(res.body.infinity_price_v4).to.equal();
  });

  it('android + Indonesia + config enabled = v3', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_m3_v3_d50',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);
  });

  it('not android + Indonesia + config enabled != v3', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('android + Canada + config enabled != v3', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'America/Toronto' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('android + no timezone + config enabled != v3', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('unknown timezone == baseline', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Manila' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal();
  });

  it('android + Indonesia + config disabled != v3', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('no flash sale', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.premiumFlashSaleEndDate = undefined;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3',
      'infinity_m6_v3',
      'infinity_m3_v3',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);

    user = await User.findById(0);
    user.config.infinity_price_v3 = false;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months',
      'boo_infinity_6_months',
      'boo_infinity_3_months',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('x variants', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x2_d50',
      'infinity_m6_v3_x2_d50',
      'infinity_m3_v3_x2_d50',
      'infinity_m1_v3_x2',
      'infinity_lifetime_v3',
    ]);

    user = await User.findById(0);
    user.config.infinity_price_v3 = false;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_lifetime',
    ]);
  });

  it('UK - baseline v3, variant null, config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Europe/London' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_m3_v3_d50',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal();
  });

  it('UK - baseline v3, variant null, config false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Europe/London' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_m3_v3_d50',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal();
  });

  it('US - baseline v3, variant v4, config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'America/New_York' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v4_d50',
      'infinity_m6_v4_d50',
      'infinity_m3_v4_d50',
      'infinity_m1_v4',
      'infinity_lifetime_v4',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal(true);
    expect(res.body.infinity_price_v6).to.equal();
  });

  it('US - baseline v3, variant v4, config false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'America/New_York' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_m3_v3_d50',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal(false);
    expect(res.body.infinity_price_v6).to.equal();
  });

  it('Vietnam - dating, config false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: [],
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant',
      'boo_infinity_6_months_discount_50_variant',
      'boo_infinity_3_months_discount_50_variant',
      'boo_infinity_1_month_variant',
      'boo_infinity_lifetime',
    ]);
  });

  it('Vietnam - dating, config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: [],
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x1_d50',
      'infinity_m6_v3_x1_d50',
      'infinity_m3_v3_x1_d50',
      'infinity_m1_v3_x1',
      'infinity_lifetime_v3',
    ]);
  });

  it('Vietnam - friends, config false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female'],
        dating: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x2_d50',
      'infinity_m6_v3_x2_d50',
      'infinity_m3_v3_x2_d50',
      'infinity_m1_v3_x2',
      'infinity_lifetime_v3',
    ]);
  });

  it('Vietnam - friends, config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female'],
        dating: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v4_x2_d50',
      'infinity_m6_v4_x2_d50',
      'infinity_m3_v4_x2_d50',
      'infinity_m1_v4_x2',
      'infinity_lifetime_v4',
    ]);
  });

  it('Iceland - baseline v1, variant v6, config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Atlantic/Reykjavik' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v6_d50',
      'infinity_m6_v6_d50',
      'infinity_m3_v6_d50',
      'infinity_m1_v6',
      'infinity_lifetime_v6',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal(true);
  });

  it('Iceland - baseline v1, variant v6, config false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Atlantic/Reykjavik' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal(false);
  });

  /*
  it('app_476 + Canada + config false', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'America/Toronto' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = false;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_lifetime',
    ]);
  });

  it('app_476 + Canada + config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'America/Toronto' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = true;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'infinity_w1',
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_lifetime',
    ]);
  });

  it('app_476 + Canada + config true + x1', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'America/Toronto' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: [],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = true;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant',
      'infinity_w1_x1',
      'boo_infinity_1_month_variant',
      'boo_infinity_3_months_discount_50_variant',
      'boo_infinity_6_months_discount_50_variant',
      'boo_infinity_lifetime',
    ]);
  });

  it('app_476 + Canada + config true + x2', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'America/Toronto' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = true;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'infinity_w1_x2',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_lifetime',
    ]);
  });

  it('app_476 + Canada + config true + x3', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'America/Toronto' });
    expect(res.status).to.equal(200);

    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = true;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'infinity_w1_x3',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_lifetime',
    ]);
  });

  it('app_476 + Vietnam + both + config false', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = false;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m1_v3',
      'infinity_m3_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_lifetime_v3',
    ]);
  });

  it('app_476 + Vietnam + both + config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = true;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_w1',
      'infinity_m1_v3',
      'infinity_m3_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_lifetime_v3',
    ]);
  });

  it('app_476 + Vietnam + friends + config false', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = false;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x2_d50',
      'infinity_m1_v3_x2',
      'infinity_m3_v3_x2_d50',
      'infinity_m6_v3_x2_d50',
      'infinity_lifetime_v3',
    ]);
  });

  it('app_476 + Vietnam + friends + config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.65', os: 'android', timezone: 'Asia/Ho_Chi_Minh' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.config.app_476).to.equal();

    user = await User.findById('0');
    user.config.app_476 = true;
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v4_x2_d50',
      'infinity_m1_v4_x2',
      'infinity_m3_v4_x2_d50',
      'infinity_m6_v4_x2_d50',
      'infinity_lifetime_v4',
    ]);
  });
  */
});

it('local density', async () => {

  sinon.stub(constants, 'getLocalDensityThreshold').returns(0);

  // user 0 joins, not enough density
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.74' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.show_sort_nearby_option).to.equal();

  // user 1 joins nearby location, enough density
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.11.74' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 21.32,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.show_sort_nearby_option).to.equal(true);

  // user 2 joins far location, not enough density
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.11.74' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 30.32,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.show_sort_nearby_option).to.equal();
});

it('global_only_premium', async () => {

  // USA
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.3', timezone: 'Pacific/Honolulu' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.global_only_premium).to.equal(false);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      global: true,
      local: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.global).to.equal(true);
  expect(res.body.user.preferences.local).to.equal(false);

  // Indonesia
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.3', timezone: 'Asia/Jakarta' })
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.global_only_premium).to.equal(true);

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      global: true,
      local: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.global).to.equal(true);
  expect(res.body.user.preferences.local).to.equal(true);

  user = await User.findOne({ _id: 1 });
  user.premiumExpiration = Date.now() + 86400000;
  await user.save();

  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 1)
    .send({
      global: true,
      local: false,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.preferences.global).to.equal(true);
  expect(res.body.user.preferences.local).to.equal(false);
});

it('first flash sale', async () => {
  clock = sinon.useFakeTimers();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.7' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale.saleEndDate).to.equal('1970-01-01T06:00:00.000Z');
  expect(res.body.user.premiumFlashSale.oneTime).to.equal();

  // sale not cancelled after exiting
  res = await request(app)
    .patch('/v1/user/events')
    .set('authorization', 0)
    .send({ exitFlashSale: true });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.7' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale.saleEndDate).to.equal('1970-01-01T06:00:00.000Z');
  expect(res.body.user.premiumFlashSale.oneTime).to.equal();

  clock.restore();
});

it('app_688 - delay first flash sale', async () => {
  clock = sinon.useFakeTimers();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.80' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale).to.equal();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.product_ids).to.eql([
    'boo_infinity_12_months',
    'boo_infinity_1_month',
    'boo_infinity_3_months',
    'boo_infinity_6_months',
    'boo_infinity_lifetime',
  ]);

  res = await request(app)
    .patch('/v1/user/events')
    .set('authorization', 0)
    .send({ exitFlashSale: true });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale.saleEndDate).to.equal('1970-01-01T06:00:00.000Z');

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.product_ids).to.eql([
    'boo_infinity_12_months_discount_50',
    'boo_infinity_1_month',
    'boo_infinity_3_months_discount_50',
    'boo_infinity_6_months_discount_50',
    'boo_infinity_lifetime',
  ]);

  clock.restore();
});

/*
it('god_mode', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById(0);
  user.config.god_mode = true;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.god_mode).to.equal(true);
  expect(res.body.subscription_products).to.eql({
    boo_infinity_v2: [
      'infinity_w1_v5',
      'infinity_m1_v5',
      'infinity_m3_v5',
      'infinity_m6_v5',
    ],
    god_mode: [
      'premium_w1_v5',
      'premium_m1_v5',
      'premium_m3_v5',
      'lifetime_v5',
    ],
  });
});
*/

it('onboarding_video_url', async () => {
  const testdata = [
    // countryLocale, gender, darkMode, use case, assignConfig, url
    ['en_US', 'female', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/en_US_black.mp4'],
    ['en_US', 'female', false, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/en_US_white.mp4'],
    ['en_US', 'female', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/en_US_black.mp4'],
    ['en_US', 'female', false, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/en_US_white.mp4'],
    ['en_US', 'female', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/en_US_black.mp4'],
    ['en_US', 'female', false, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/en_US_white.mp4'],
    ['en_US', 'male', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video/en_US_black.mp4'],
    ['en_US', 'male', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_men_dating/en_US_black.mp4'],
    ['en_US', 'male', true, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/en_US_black.mp4'],
    ['en_US', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/en_US_black.mp4'],
    ['en_GB', 'male', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video/en_GB_black.mp4'],
    ['en_GB', 'male', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video/en_GB_black.mp4'],
    ['en_GB', 'male', true, 'friends', true, null],
    ['en_GB', 'female', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/en_black.mp4'],
    ['en_GB', 'female', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/en_black.mp4'],
    ['en_GB', 'female', true, 'friends', false, null],
    ['en_GB', 'female', true, 'friends', true, null],
    ['en_NG', 'male', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video/en_african_black.mp4'],
    ['en_CA', 'male', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video/en_black.mp4'],
    ['en_CA', 'male', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_men_dating/en_black.mp4'],
    ['en_CA', 'male', true, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/en_black.mp4'],
    ['en_CA', 'male', false, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/en_white.mp4'],
    ['de_DE', 'male', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_v2/de.mp4'],
    ['de_DE', 'male', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_v2/de.mp4'],
    ['de_DE', 'male', true, 'friends', false, null],
    ['de_DE', 'male', true, 'friends', true, null],
    ['is_IS', 'male', true, 'dating', false, null],
    ['is_IS', 'male', true, 'friends', false, null],
    ['is_IS', 'male', true, 'friends', true, null],
    ['fr_GA', 'male', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_v2/fr.mp4'],
    ['zh_Hant_TW', 'male', true, 'dating', true, null],
    ['zh_Hant_TW', 'male', true, 'dating', false, null],
    ['zh_Hant', 'male', true, 'dating', true, null],
    ['zh_Hant_HK', 'male', true, 'dating', true, null],
    ['nn_NO', 'male', true, 'friends', false, null],
    ['nn_NO', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/no_black.mp4'],
    ['nb_NO', 'male', true, 'friends', false, null],
    ['nb_NO', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/no_black.mp4'],
    ['zh_Hant_TW', 'male', true, 'friends', false, null],
    ['zh_Hant_TW', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/zh_Hant_black.mp4'],
    ['zh_Hans_CN', 'male', true, 'friends', false, null],
    ['zh_Hans_CN', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/zh_Hans_black.mp4'],
    ['he_IL', 'male', true, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/he_black.mp4'],
    ['he_IL', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/he_black.mp4'],
    ['ru_RU', 'female', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/ru_black.mp4'],
    ['ru_RU', 'female', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_women_dating/ru_black.mp4'],
    ['ru_RU', 'male', true, 'dating', false, null],
    ['ru_RU', 'male', true, 'dating', true, null],
    ['ru_RU', 'female', true, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/ru_black.mp4'],
    ['ru_RU', 'female', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/ru_black.mp4'],
    ['ru_RU', 'male', true, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/ru_black.mp4'],
    ['ru_RU', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/ru_black.mp4'],
    ['ko_KR', 'male', true, 'friends', false, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/ko_black.mp4'],
    ['ko_KR', 'male', true, 'friends', true, 'MOCK_IMAGE_DOMAIN/onboarding_video_friends_v2/ko_black.mp4'],
    ['uk_UA', 'female', true, 'dating', false, null],
    ['uk_UA', 'female', true, 'dating', true, null],
    ['nl_NL', 'female', true, 'dating', false, 'MOCK_IMAGE_DOMAIN/onboarding_video/nl_black.mp4'],
    ['nl_NL', 'female', true, 'dating', true, 'MOCK_IMAGE_DOMAIN/onboarding_video/nl_black.mp4'],
  ];

  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    const countryLocale = data[0];
    const gender = data[1];
    const darkMode = data[2];
    const useCase = data[3];
    const assignConfig = data[4];
    const url = data[5];
    console.log(i, data);

    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(assignConfig);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.17', countryLocale, darkMode })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender })
    expect(res.status).to.equal(200);

    if (useCase == 'dating') {
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', 0)
        .send({
          friends: [],
          dating: ['female'],
        });
      expect(res.status).to.equal(200);
    }
    if (useCase == 'friends') {
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', 0)
        .send({
          friends: ['female'],
          dating: [],
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal(url);

    await User.deleteMany();
  }
});

describe('app_648 niche customization', () => {
  it('kochava weightlifting campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.80',
        kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || Interest Lifting || App || Purchase' },
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('sports');
  });

  it('appsflyer weightlifting campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.80',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      payload: {
        adgroup_id: "120216245391630350",
        retargeting_conversion_type: "none",
        is_fb: true,
        is_first_launch: false,
        iscache: false,
        click_time: "2025-03-16 05:19:28.000",
        adset: "C. Brazil || AAA || Installs   Ad set",
        match_type: "gp_referrer",
        af_channel: "Instagram",
        is_paid: false,
        campaign_id: "120219932664700350",
        install_time: "2025-03-16 05:22:05.699",
        agency: null,
        media_source: "Facebook Ads",
        af_siteid: null,
        af_status: "Non-organic",
        af_sub1: null,
        ad_id: "120216245413750350",
        af_sub5: null,
        af_sub4: null,
        af_sub3: null,
        af_sub2: null,
        adset_id: "120212179577380350",
        http_referrer: null,
        campaign: "C. US L. English || Interest Lifting || App || Purchase",
        is_mobile_data_terms_signed: false,
        adgroup: "Portuguese (Brazilian) - CantSleep - Dating.mp4 - Copy"
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('sports');
  });

  it('appsflyer influencer kpop campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.80',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      "payload": {
        "redirect_response_data": null,
        "adgroup_id": null,
        "engmnt_source": null,
        "is_incentivized": "false",
        "retargeting_conversion_type": "none",
        "orig_cost": "0.0",
        "is_first_launch": false,
        "af_click_lookback": "7d",
        "CB_preload_equal_priority_enabled": false,
        "af_web_dp": "https://boo.world/u/anime",
        "af_cpi": null,
        "iscache": true,
        "click_time": "2025-04-09 14:01:36.398",
        "is_branded_link": null,
        "adset": null,
        "match_type": "gp_referrer",
        "af_channel": "Influencer - IG - K-pop - sa1hil_ae",
        "campaign_id": null,
        "shortlink": "sa1hilae",
        "af_pmod_lookback_window": "15m",
        "af_dp": "boo://enterprises.dating.boo",
        "install_time": "2025-04-09 14:02:15.105",
        "fbclid": "PAZXh0bgNhZW0CMTEAAaeNB-9siHqyP6nlHcKS2mJ8s4JRviRMmOaB2p6nJb_s-CLXhcpz-FDwlK29Tw_aem_al22SacsfD0YNlaYM1mLIg",
        "agency": null,
        "media_source": "Influencer - K-pop",
        "af_siteid": null,
        "af_status": "Non-organic",
        "af_sub1": null,
        "cost_cents_USD": "0",
        "af_sub5": null,
        "af_sub4": null,
        "af_sub3": null,
        "af_sub2": null,
        "adset_id": null,
        "esp_name": null,
        "http_referrer": "https://l.instagram.com/",
        "campaign": "Influencer",
        "is_universal_link": null,
        "is_retargeting": "false",
        "adgroup": null
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('music');

    user = await User.findById('0');
    expect(user.partnerCampaign).to.equal('music');
    expect(user.partnerCampaignSubniche).to.equal('k-pop');
  });

  it('appsflyer art campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.80',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      payload: {
        adgroup_id: "120216245391630350",
        retargeting_conversion_type: "none",
        is_fb: true,
        is_first_launch: false,
        iscache: false,
        click_time: "2025-03-16 05:19:28.000",
        adset: "C. Brazil || AAA || Installs   Ad set",
        match_type: "gp_referrer",
        af_channel: "Instagram",
        is_paid: false,
        campaign_id: "120219932664700350",
        install_time: "2025-03-16 05:22:05.699",
        agency: null,
        media_source: "Facebook Ads",
        af_siteid: null,
        af_status: "Non-organic",
        af_sub1: null,
        ad_id: "120216245413750350",
        af_sub5: null,
        af_sub4: null,
        af_sub3: null,
        af_sub2: null,
        adset_id: "120212179577380350",
        http_referrer: null,
        campaign: "C. US L. English || Interest Art || App || Purchase",
        is_mobile_data_terms_signed: false,
        adgroup: "Portuguese (Brazilian) - CantSleep - Dating.mp4 - Copy"
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('art');
  });

  it('appsflyer influencer art campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.80',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      "payload": {
        "redirect_response_data": null,
        "adgroup_id": null,
        "engmnt_source": null,
        "is_incentivized": "false",
        "retargeting_conversion_type": "none",
        "orig_cost": "0.0",
        "is_first_launch": false,
        "af_click_lookback": "7d",
        "CB_preload_equal_priority_enabled": false,
        "af_web_dp": "https://boo.world/u/anime",
        "af_cpi": null,
        "iscache": true,
        "click_time": "2025-04-09 14:01:36.398",
        "is_branded_link": null,
        "adset": null,
        "match_type": "gp_referrer",
        "af_channel": "Influencer - IG - Art - sa1hil_ae",
        "campaign_id": null,
        "shortlink": "sa1hilae",
        "af_pmod_lookback_window": "15m",
        "af_dp": "boo://enterprises.dating.boo",
        "install_time": "2025-04-09 14:02:15.105",
        "fbclid": "PAZXh0bgNhZW0CMTEAAaeNB-9siHqyP6nlHcKS2mJ8s4JRviRMmOaB2p6nJb_s-CLXhcpz-FDwlK29Tw_aem_al22SacsfD0YNlaYM1mLIg",
        "agency": null,
        "media_source": "Influencer - Art",
        "af_siteid": null,
        "af_status": "Non-organic",
        "af_sub1": null,
        "cost_cents_USD": "0",
        "af_sub5": null,
        "af_sub4": null,
        "af_sub3": null,
        "af_sub2": null,
        "adset_id": null,
        "esp_name": null,
        "http_referrer": "https://l.instagram.com/",
        "campaign": "Influencer",
        "is_universal_link": null,
        "is_retargeting": "false",
        "adgroup": null
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('art');
  });

  it('smart should not be matched as art', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.80',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      payload: {
        adgroup_id: "120216245391630350",
        retargeting_conversion_type: "none",
        is_fb: true,
        is_first_launch: false,
        iscache: false,
        click_time: "2025-03-16 05:19:28.000",
        adset: "C. Brazil || AAA || Installs   Ad set",
        match_type: "gp_referrer",
        af_channel: "Instagram",
        is_paid: false,
        campaign_id: "120219932664700350",
        install_time: "2025-03-16 05:22:05.699",
        agency: null,
        media_source: "Facebook Ads",
        af_siteid: null,
        af_status: "Non-organic",
        af_sub1: null,
        ad_id: "120216245413750350",
        af_sub5: null,
        af_sub4: null,
        af_sub3: null,
        af_sub2: null,
        adset_id: "120212179577380350",
        http_referrer: null,
        campaign: "Smart+ || C. Serbia L. Serbian || App || Purchase || Android",
        is_mobile_data_terms_signed: false,
        adgroup: "Portuguese (Brazilian) - CantSleep - Dating.mp4 - Copy"
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal();
  });
});

describe('app_662 onboarding video for anime and gaming', () => {
  beforeEach(async () => {
    await Interest.insertMany([
      { interest: '#playstationgamer', name: 'playstationgamer' },
      { interest: '#inuyasha', name: 'inuyasha' },
    ]);
  });

  it('male, English, added gaming interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_gaming/en_white.mp4');
  });

  it('male, English, added anime interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['inuyasha'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('male, English, added gaming interest, dark mode', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
        darkMode: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_gaming/en_black.mp4');
  });

  it('male, English, added anime interest, dark mode', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
        darkMode: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['inuyasha'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_black.mp4');
  });

  it('male, Japanese, added gaming interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'ja',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.not.equal('MOCK_IMAGE_DOMAIN/onboarding_video_gaming/en_white.mp4');
  });

  it('male, Japanese, added anime interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'ja',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['inuyasha'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.not.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('female, English, added gaming interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'female' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_gaming_women).to.equal();
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_gaming_women/en_white.mp4');
  });

  it('female, English, added anime interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'female' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['inuyasha'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('male, English, added both gaming and anime interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer', 'inuyasha'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('male, English, anime ad campaign and gaming interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('male, English, gaming ad campaign and anime interest', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
        kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || Interest Gaming || App || Purchase' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['inuyasha'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_gaming/en_white.mp4');
  });

  it('male, English, anime ad campaign via appsflyer', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      payload: {
        adgroup_id: "120216245391630350",
        retargeting_conversion_type: "none",
        is_fb: true,
        is_first_launch: false,
        iscache: false,
        click_time: "2025-03-16 05:19:28.000",
        adset: "C. Brazil || AAA || Installs   Ad set",
        match_type: "gp_referrer",
        af_channel: "Instagram",
        is_paid: false,
        campaign_id: "2034403665961700206",
        install_time: "2025-03-16 05:22:05.699",
        agency: null,
        media_source: "Facebook Ads",
        af_siteid: null,
        af_status: "Non-organic",
        af_sub1: null,
        ad_id: "120216245413750350",
        af_sub5: null,
        af_sub4: null,
        af_sub3: null,
        af_sub2: null,
        adset_id: "120212179577380350",
        http_referrer: null,
        campaign: "C. US L. English || Interest Anime || App || Purchase",
        is_mobile_data_terms_signed: false,
        adgroup: "Portuguese (Brazilian) - CantSleep - Dating.mp4 - Copy"
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);
    expect(res.body.partnerCampaign).to.equal('anime');

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('anime');

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('male, English, anime ad campaign via appsflyer influencer campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      "payload": {
        "redirect_response_data": null,
        "adgroup_id": null,
        "engmnt_source": null,
        "is_incentivized": "false",
        "retargeting_conversion_type": "none",
        "orig_cost": "0.0",
        "is_first_launch": false,
        "af_click_lookback": "7d",
        "CB_preload_equal_priority_enabled": false,
        "af_web_dp": "https://boo.world/u/anime",
        "af_cpi": null,
        "iscache": true,
        "click_time": "2025-04-09 14:01:36.398",
        "is_branded_link": null,
        "adset": null,
        "match_type": "gp_referrer",
        "af_channel": "Influencer - IG - Anime - sa1hil_ae",
        "campaign_id": null,
        "shortlink": "sa1hilae",
        "af_pmod_lookback_window": "15m",
        "af_dp": "boo://enterprises.dating.boo",
        "install_time": "2025-04-09 14:02:15.105",
        "fbclid": "PAZXh0bgNhZW0CMTEAAaeNB-9siHqyP6nlHcKS2mJ8s4JRviRMmOaB2p6nJb_s-CLXhcpz-FDwlK29Tw_aem_al22SacsfD0YNlaYM1mLIg",
        "agency": null,
        "media_source": "Influencer - Anime",
        "af_siteid": null,
        "af_status": "Non-organic",
        "af_sub1": null,
        "cost_cents_USD": "0",
        "af_sub5": null,
        "af_sub4": null,
        "af_sub3": null,
        "af_sub2": null,
        "adset_id": null,
        "esp_name": null,
        "http_referrer": "https://l.instagram.com/",
        "campaign": "Influencer",
        "is_universal_link": null,
        "is_retargeting": "false",
        "adgroup": null
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['playstationgamer'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('anime');

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_white.mp4');
  });

  it('male, Korean, anime ad campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'ko',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/ko_white.mp4');
  });

  it('male, British English, anime ad campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
        countryLocale: 'en_GB',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/en_GB_white.mp4');
  });

  it('zh_Hant_HK, anime ad campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'zh-Hant',
        countryLocale: 'zh_Hant_HK',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/zh_Hant_HK_white.mp4');
  });

  it('sr_Latn_RS, anime ad campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'sr',
        countryLocale: 'sr_Latn_RS',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_anime/sr_white.mp4');
  });

  it('male, Japanese, anime ad campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'ja',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal(null);
  });

  it('male, Armenian, anime ad campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'hy',
        kochava: { network: 'fb', partner_campaign_name: 'C. UK L. English || Interest Anime || Purchase || Android' },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal(null);
  });

  it('English, lifting ad campaign via appsflyer influencer campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      "payload": {
        "redirect_response_data": null,
        "adgroup_id": null,
        "engmnt_source": null,
        "is_incentivized": "false",
        "retargeting_conversion_type": "none",
        "orig_cost": "0.0",
        "is_first_launch": false,
        "af_click_lookback": "7d",
        "CB_preload_equal_priority_enabled": false,
        "af_web_dp": "https://boo.world/u/anime",
        "af_cpi": null,
        "iscache": true,
        "click_time": "2025-04-09 14:01:36.398",
        "is_branded_link": null,
        "adset": null,
        "match_type": "gp_referrer",
        "af_channel": "Influencer - IG - Lifting - sa1hil_ae",
        "campaign_id": null,
        "shortlink": "sa1hilae",
        "af_pmod_lookback_window": "15m",
        "af_dp": "boo://enterprises.dating.boo",
        "install_time": "2025-04-09 14:02:15.105",
        "fbclid": "PAZXh0bgNhZW0CMTEAAaeNB-9siHqyP6nlHcKS2mJ8s4JRviRMmOaB2p6nJb_s-CLXhcpz-FDwlK29Tw_aem_al22SacsfD0YNlaYM1mLIg",
        "agency": null,
        "media_source": "Influencer - Lifting",
        "af_siteid": null,
        "af_status": "Non-organic",
        "af_sub1": null,
        "cost_cents_USD": "0",
        "af_sub5": null,
        "af_sub4": null,
        "af_sub3": null,
        "af_sub2": null,
        "adset_id": null,
        "esp_name": null,
        "http_referrer": "https://l.instagram.com/",
        "campaign": "Influencer",
        "is_universal_link": null,
        "is_retargeting": "false",
        "adgroup": null
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('sports');

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_lifting/en_white.mp4');
  });

  it('English, art ad campaign via appsflyer influencer campaign', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({
        appVersion: '1.13.79',
        locale: 'en',
      });
    expect(res.status).to.equal(200);

    const appsflyerDataFB = {
      status: 'success',
      "payload": {
        "redirect_response_data": null,
        "adgroup_id": null,
        "engmnt_source": null,
        "is_incentivized": "false",
        "retargeting_conversion_type": "none",
        "orig_cost": "0.0",
        "is_first_launch": false,
        "af_click_lookback": "7d",
        "CB_preload_equal_priority_enabled": false,
        "af_web_dp": "https://boo.world/u/anime",
        "af_cpi": null,
        "iscache": true,
        "click_time": "2025-04-09 14:01:36.398",
        "is_branded_link": null,
        "adset": null,
        "match_type": "gp_referrer",
        "af_channel": "Influencer - IG - Art - sa1hil_ae",
        "campaign_id": null,
        "shortlink": "sa1hilae",
        "af_pmod_lookback_window": "15m",
        "af_dp": "boo://enterprises.dating.boo",
        "install_time": "2025-04-09 14:02:15.105",
        "fbclid": "PAZXh0bgNhZW0CMTEAAaeNB-9siHqyP6nlHcKS2mJ8s4JRviRMmOaB2p6nJb_s-CLXhcpz-FDwlK29Tw_aem_al22SacsfD0YNlaYM1mLIg",
        "agency": null,
        "media_source": "Influencer - Art",
        "af_siteid": null,
        "af_status": "Non-organic",
        "af_sub1": null,
        "cost_cents_USD": "0",
        "af_sub5": null,
        "af_sub4": null,
        "af_sub3": null,
        "af_sub2": null,
        "adset_id": null,
        "esp_name": null,
        "http_referrer": "https://l.instagram.com/",
        "campaign": "Influencer",
        "is_universal_link": null,
        "is_retargeting": "false",
        "adgroup": null
      }
    }

    res = await request(app)
      .put('/v1/user/appsflyer')
      .set('authorization', 0)
      .send({ appsflyer: appsflyerDataFB });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
    expect(res.status).to.equal(200);
    expect(res.body.user.partnerCampaign).to.equal('art');

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.onboarding_video_url).to.equal('MOCK_IMAGE_DOMAIN/onboarding_video_art/en_white.mp4');
  });
});

it('use_pusher_ios', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({os: 'android'})
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_pusher_ios).to.equal();

  user = await User.findById('0');
  user.os = 'ios';
  user.createdAt = 0;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_pusher_ios).to.equal(true);

  user.createdAt = 1;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_pusher_ios).to.equal(true);

  user.config.use_pusher_ios = true;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_pusher_ios).to.equal(true);
});

it('partnerships', async () => {
  const numPartners = 26;

  // USA
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.20' })
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.partnerships).to.equal();

  res = await request(app)
    .get('/v1/config/partnerships')
    .set('authorization', 0)
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.partnerships.length).to.equal(0);

  // India
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.20' })
    .set('X-Forwarded-For', '***********')
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1)
    .set('X-Forwarded-For', '***********')
  expect(res.status).to.equal(200);
  expect(res.body.partnerships).to.equal(true);

  res = await request(app)
    .get('/v1/config/partnerships')
    .set('authorization', 1)
    .set('X-Forwarded-For', '***********')
  expect(res.status).to.equal(200);
  console.log(res.body);
  expect(res.body.partnerships.length).to.equal(numPartners);
  expect(res.body.partnerships[0]).to.eql({
    brand: 'Redwolf',
    geography: 'India',
    category: 'Shopping',
    perk: 'Flat 10% off on Redwolf and official merchandise',
    coupon_code: 'RWBOO',
    instructions: '',
    rules: "1. Offer valid once for all users on a minimum purchase of ₹999.\r\n2. Offer valid on all Redwolf and official merchandise but not applicable to third-party products under the 'Artist Merch' section.\r\n3. Offer valid till 31st December 2023.\r\n4. Coupons cannot be clubbed with another coupon code or offer.\r\n5. Offer not valid during sitewide/seasonal sales on the website or on products already on discount or clearance sale.\r\n6. The maximum discount that can be availed is ₹150.",
    url: 'https://www.redwolf.in/',
    logo: 'MOCK_IMAGE_DOMAIN/partner_logos/redwolf.png'
  });

  user = await User.findById('1');
  expect(user.events.viewed_perks).to.equal(1);

  // web routes
  res = await request(app)
    .get('/web/partnerships')
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.partnerships.length).to.equal(0);

  res = await request(app)
    .get('/web/partnerships')
    .set('X-Forwarded-For', '***********')
  expect(res.status).to.equal(200);
  expect(res.body.partnerships.length).to.equal(numPartners);
});

describe('ios pricing experiments', () => {
  beforeEach(async () => {
    sinon.stub(pricingExperimentLib, 'getIosCountryData').callsFake((countryName) => {
      const countryData = {
        Japan: {
          college: {
            baseline: 1,
            variant: 2,
          },
          dating: {
            baseline: 1,
            variant: 2,
          },
          friends: {
            baseline: 1,
            variant: 2,
          },
          both: {
            baseline: 1,
            variant: 2,
          },
        },
        Indonesia: {
          college: {
            baseline: 1,
            variant: null,
          },
          dating: {
            baseline: 1,
            variant: null,
          },
          friends: {
            baseline: 1,
            variant: null,
          },
          both: {
            baseline: 1,
            variant: null,
          },
        },
        Brazil: {
          college: {
            baseline: 2,
            variant: null,
          },
          dating: {
            baseline: 2,
            variant: null,
          },
          friends: {
            baseline: 2,
            variant: null,
          },
          both: {
            baseline: 2,
            variant: null,
          },
        },
        Germany: {
          college: {
            baseline: 1,
            variant: 3,
          },
          dating: {
            baseline: 1,
            variant: 3,
          },
          friends: {
            baseline: 1,
            variant: 3,
          },
          both: {
            baseline: 1,
            variant: 3,
          },
        },
      };
      return countryData[countryName];
    });
  });

  it('config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'ios', timezone: 'Asia/Tokyo' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v2).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v2_d50',
      'infinity_m6_v2_d50',
      'infinity_m3_v2_d50',
      'infinity_m1_v2',
      'infinity_lifetime_V2',
    ]);
  });

  it('config false', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'ios', timezone: 'Asia/Tokyo' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v2).to.equal(false);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('shipped baseline', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'ios', timezone: 'Asia/Jakarta' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v2).to.equal();
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_1_month',
      'boo_infinity_lifetime',
    ]);
  });

  it('shipped variant', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'ios', timezone: 'America/Sao_Paulo' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v2).to.equal();
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v2_d50',
      'infinity_m6_v2_d50',
      'infinity_m3_v2_d50',
      'infinity_m1_v2',
      'infinity_lifetime_V2',
    ]);
  });

  it('app_374 v1', async () => {
    // Indonesia - shipped v1
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'ios', appVersion: '1.13.57', timezone: 'Asia/Jakarta'});
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50',
      'infinity_w1_v1',
      'boo_infinity_1_month',
      'boo_infinity_3_months_discount_50',
      'boo_infinity_6_months_discount_50',
      'boo_infinity_lifetime',
    ]);

    // dating only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant',
      'infinity_w1_v1_x1',
      'boo_infinity_1_month_variant',
      'boo_infinity_3_months_discount_50_variant',
      'boo_infinity_6_months_discount_50_variant',
      'boo_infinity_lifetime',
    ]);

    // friends only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'boo_infinity_12_months_discount_50_variant_1',
      'infinity_w1_v1_x2',
      'boo_infinity_1_month_variant_1',
      'boo_infinity_3_months_discount_50_variant_1',
      'boo_infinity_6_months_discount_50_variant_1',
      'boo_infinity_lifetime',
    ]);
  });

  it('app_374 v2', async () => {
    // Brazil - shipped v2
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ os: 'ios', appVersion: '1.13.57', timezone: 'America/Sao_Paulo'});
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v2_d50',
      'infinity_w1_v2',
      'infinity_m1_v2',
      'infinity_m3_v2_d50',
      'infinity_m6_v2_d50',
      'infinity_lifetime_V2',
    ]);

    // dating only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: [],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v2_x1_d50',
      'infinity_w1_v2_x1',
      'infinity_m1_v2_x1',
      'infinity_m3_v2_x1_d50',
      'infinity_m6_v2_x1_d50',
      'infinity_lifetime_V2',
    ]);

    // friends only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v2_x2_d50',
      'infinity_w1_v2_x2',
      'infinity_m1_v2_x2',
      'infinity_m3_v2_x2_d50',
      'infinity_m6_v2_x2_d50',
      'infinity_lifetime_V2',
    ]);
  });

  it('v3 config true - version prior to 1.13.52', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'ios', timezone: 'Europe/Berlin' });
    expect(res.status).to.equal(200);

    // both dating and friends
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', 0)
      .send({ answers: [] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_m3_v3_d50',
      'infinity_m1_v3',
      'infinity_lifetime_v3',
    ]);

    // friends only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      "boo_infinity_12_months_discount_50_variant_1",
      "boo_infinity_6_months_discount_50_variant_1",
      "boo_infinity_3_months_discount_50_variant_1",
      "boo_infinity_1_month_variant_1",
      "boo_infinity_lifetime",
    ]);

    // college variant
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x3_d50',
      'infinity_m6_v3_x3_d50',
      'infinity_m3_v3_x3_d50',
      'infinity_m1_v3_x3',
      'infinity_lifetime_v3',
    ]);
  });

  it('v3 config true - version 1.13.52', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.52', os: 'ios', timezone: 'Europe/Berlin' });
    expect(res.status).to.equal(200);

    // both dating and friends
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', 0)
      .send({ answers: [] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_m1_v3',
      'infinity_m3_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_lifetime_v3',
    ]);

    // friends only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'Infinity_m12_v3_x2_d50',
      'Infinity_m1_v3_x2',
      'Infinity_m3_v3_x2_d50',
      'Infinity_m6_v3_x2_d50',
      'infinity_lifetime_v3',
    ]);

    // college variant
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x3_d50',
      'infinity_m1_v3_x3',
      'infinity_m3_v3_x3_d50',
      'infinity_m6_v3_x3_d50',
      'infinity_lifetime_v3',
    ]);

  });

  it('v3 config true - version 1.13.57', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.57', os: 'ios', timezone: 'Europe/Berlin' });
    expect(res.status).to.equal(200);

    // both dating and friends
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/quizAnswers')
      .set('authorization', 0)
      .send({ answers: [] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_d50',
      'infinity_w1_v3',
      'infinity_m1_v3',
      'infinity_m3_v3_d50',
      'infinity_m6_v3_d50',
      'infinity_lifetime_v3',
    ]);

    // friends only
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: [],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'Infinity_m12_v3_x2_d50',
      'infinity_w1_v3_x2',
      'Infinity_m1_v3_x2',
      'Infinity_m3_v3_x2_d50',
      'Infinity_m6_v3_x2_d50',
      'infinity_lifetime_v3',
    ]);

    // college variant
    user = await User.findById(0);
    user.birthday = new Date(new Date().getFullYear() - 20, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.ios_infinity_price_v3).to.equal(true);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v3_x3_d50',
      'infinity_w1_v3_x3',
      'infinity_m1_v3_x3',
      'infinity_m3_v3_x3_d50',
      'infinity_m6_v3_x3_d50',
      'infinity_lifetime_v3',
    ]);

  });
});

describe('country pricing experiments without stub', async () => {
  it('Iceland - baseline v6, variant null, config true', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(true);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Atlantic/Reykjavik' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v6_d50',
      'infinity_m6_v6_d50',
      'infinity_m3_v6_d50',
      'infinity_m1_v6',
      'infinity_lifetime_v6',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal();
  });

  it('Iceland - baseline v6, variant null, config false', async () => {
    basic.assignConfig.restore();
    sinon.stub(basic, 'assignConfig').returns(false);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.61', os: 'android', timezone: 'Atlantic/Reykjavik' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['male'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.product_ids).to.eql([
      'infinity_m12_v6_d50',
      'infinity_m6_v6_d50',
      'infinity_m3_v6_d50',
      'infinity_m1_v6',
      'infinity_lifetime_v6',
    ]);
    expect(res.body.infinity_price_v3).to.equal();
    expect(res.body.infinity_price_v4).to.equal();
    expect(res.body.infinity_price_v6).to.equal();
  });
});

it('send_likes_verified_only', async () => {
  const testdata = [
    // appVersion, timezone, result
    ['1.13.36', 'Asia/Manila', true],
    ['1.13.36', 'Asia/Tokyo', false],
    ['1.13.35', 'Asia/Manila', false],
  ];

  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    const appVersion = data[0];
    const timezone = data[1];
    const result = data[2];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({appVersion, timezone})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    if (result) {
      expect(res.body.send_likes_verified_only).to.equal(true);
    } else {
      expect(res.body.send_likes_verified_only).to.equal();
    }

    await User.deleteMany();
  }
});

it('use_gpt4', async () => {
  const testdata = [
    // os, timezone, result
    ['ios', 'Asia/Phnom_Penh', true],
    ['ios', 'Asia/Tokyo', true],
    ['android', 'Asia/Phnom_Penh', false],
    ['android', 'Asia/Tokyo', true],
  ];

  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    const os = data[0];
    const timezone = data[1];
    const result = data[2];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({os, timezone, appVersion:'1.13.24'})
    expect(res.status).to.equal(200);

    const use_gpt4 = openai.useGpt4((await User.findById(0)));
    if (result) {
      expect(use_gpt4).to.equal(true);
    } else {
      expect(use_gpt4).to.equal(false);
    }

    await User.deleteMany();
  }
});

it('send_keywords_to_admob', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ os: 'android' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.send_keywords_to_admob).to.equal(true);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ os: 'ios' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.send_keywords_to_admob).to.equal(false);
});

it('use_tenor', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  user.createdAt = 0;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_tenor).to.equal(true);

  user = await User.findById('0');
  user.createdAt = 1;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_tenor).to.equal(true);
});

it('app_235', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.48'});
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.product_ids).to.eql([
    'boo_infinity_12_months_discount_50',
    'boo_infinity_1_month',
    'boo_infinity_3_months_discount_50',
    'boo_infinity_6_months_discount_50',
    'boo_infinity_lifetime',
  ]);
});

it('app_885', async () => {
  // no weekly subscription
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.95', os: 'android'});
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.product_ids).to.eql([
    'boo_infinity_lifetime',
    'boo_infinity_12_months',
    'boo_infinity_3_months',
    'boo_infinity_1_month',
    'boo_infinity_6_months',
  ]);

  // with weekly subscription
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.95', os: 'ios'});
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.product_ids).to.eql([
    'infinity_w1_v1',
    'boo_infinity_lifetime',
    'boo_infinity_12_months',
    'boo_infinity_3_months',
    'boo_infinity_1_month',
    'boo_infinity_6_months',
  ]);
});

describe('app_300', () => {
  describe('boost functional', () => {
    let clockFake;
    beforeEach(async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({
          appVersion: '1.13.50',
        });
      expect(res.status).to.equal(200);

      const today = new Date();
      today.setHours(10); // Set the hour to 10 am avoid flashsale
      clockFake = sinon.useFakeTimers(today.getTime());
    });

    afterEach(() => {
      clockFake.restore();
    });

    it('get boost products no sale no variant', async () => {
      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_product_ids).to.eql([
        'boost_10_v1',
        'boost_5_v1',
        'boost_1_v1',
      ]);
    })

    it('get boost products no sale variant dating', async () => {

      user = await User.findById('0');
      user.preferences.dating = ['female']
      await user.save()
      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_product_ids).to.eql([
        'boost_10_v1_x1',
        'boost_5_v1_x1',
        'boost_1_v1_x1',
      ]);
    })

    it('get boost products no sale variant friends', async () => {
      user = await User.findById('0');
      user.preferences.friends = ['male', 'female']
      await user.save()
      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_product_ids).to.eql([
        'boost_10_v1_x2',
        'boost_5_v1_x2',
        'boost_1_v1_x2',
      ]);
    })

    it('get boost products no sale variant college', async () => {
      const today = new Date();

      const yearsBefore = 22;

      user = await User.findById('0');
      user.birthday = today.setFullYear(today.getFullYear() - yearsBefore);
      user.preferences.dating = ['female']
      user.preferences.friends = ['male', 'female']
      await user.save()
      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_product_ids).to.eql([
        'boost_10_v1_x3',
        'boost_5_v1_x3',
        'boost_1_v1_x3',
      ]);
    })

    it('get boost products no sale variant both', async () => {
      user = await User.findById('0');
      user.preferences.dating = ['female']
      user.preferences.friends = ['male', 'female']
      await user.save()

      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_product_ids).to.eql([
        'boost_10_v1',
        'boost_5_v1',
        'boost_1_v1',
      ]);
    })


    it('get boost products sale variant both', async () => {
      clockFake.restore();
      const today = new Date();
      const threeDaysAgo = new Date(today.getTime() - (3 * 24 * 60 * 60 * 1000)); // Calculate 3 days before

      user = await User.findById('0');
      // user.birthday = today.setFullYear(today.getFullYear() - yearsBefore);
      user.createdAt = threeDaysAgo;
      user.preferences.dating = ['female']
      user.preferences.friends = ['male', 'female']
      await user.save()

      console.log('created at :', user.createdAt)

      // today.setHours(19); // Set the hour to 7 pm
      // sinon.useFakeTimers(today.getTime());

      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_product_ids).to.eql([
        'boost_10_v1',
        'boost_5_v1',
        'boost_1_v1',
      ]);

      expect(res.body.boosts_discounted_product_ids).to.eql([
        'boost_10_v1_d50',
        'boost_5_v1_d50',

      ]);
      expect(res.body.boosts_discount).to.equal(50);
      expect(res.body.boosts_sale_end_date).to.not.equal();
    })

    it('purchase boost', async () => {
      clockFake.restore();

      let receipt = iapHelper.getValidAppleReceipt('boosts_5_v1', Date.now());
      res = await request(app)
        .put('/v1/boosts/purchase')
        .set('authorization', 0)
        .send({
          receipt,
          price: 3400,
          currency: 'JPY',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body.user.numBoosts).to.equal(5);

      receipts = await BoostPurchaseReceipt.find();
      expect(receipts.length).to.equal(1);
      expect(receipts[0].revenue).to.equal(34);
      expect(receipts[0].price).to.equal(3400);
      expect(receipts[0].currency).to.equal('JPY');
      expect(receipts[0].purchaseNumber).to.equal(1);

      user = await User.findById(0).lean();
      expect(user.purchases).to.eql([{
        productType: 'boost',
        productId: 'boosts_5_v1',
        daysOnPlatformBeforePurchase: 0,
        revenue: 34,
      }]);

      user = await User.findById('0');
      expect(user.metrics.numBoostsPurchased).to.equal(5);
      expect(user.metrics.numBoostPurchases).to.equal(1);
      expect(user.metrics.boostRevenue).to.equal(34);
      expect(user.metrics.revenue).to.equal(34);

      receipt = iapHelper.getValidGoogleReceipt('boosts_5_v1', Date.now());
      res = await request(app)
        .put('/v1/boosts/purchase')
        .set('authorization', 0)
        .send({
          receipt,
          price: 3400,
          currency: 'JPY',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
      expect(res.status).to.equal(200);
      expect(res.body.user.numBoosts).to.equal(10);



      receipts = await BoostPurchaseReceipt.find().sort('-createdAt');
      expect(receipts.length).to.equal(2);
      expect(receipts[0].revenue).to.equal(34);
      expect(receipts[0].price).to.equal(3400);
      expect(receipts[0].currency).to.equal('JPY');
      expect(receipts[0].purchaseNumber).to.equal(2);

      user = await User.findById('0');
      expect(user.metrics.numBoostsPurchased).to.equal(10);
      expect(user.metrics.numBoostPurchases).to.equal(2);
      expect(user.metrics.boostRevenue).to.equal(68);
      expect(user.metrics.revenue).to.equal(68);

      // check purchase receipt
      user = await User.findOne({ _id: 0 });
      user.admin = true;
      user.adminPermissions = { all: true };
      await user.save();

      res = await request(app)
        .get('/v1/admin/user/boostPurchases')
        .set('authorization', 0)
        .query({ user: '0' });
      expect(res.status).to.equal(200);
      expect(res.body.purchases.length).to.equal(2);
      expect(res.body.purchases[0].productId).to.equal('boosts_5_v1');
      expect(res.body.purchases[1].productId).to.equal('boosts_5_v1');
    });

    it('use boost', async () => {
      //purchase
      receipt = iapHelper.getValidGoogleReceipt('boosts_5_v1', Date.now());
      res = await request(app)
        .put('/v1/boosts/purchase')
        .set('authorization', 0)
        .send({
          receipt,
          price: 3400,
          currency: 'JPY',
        });
      expect(res.status).to.equal(200);

      // 1st use
      res = await request(app)
        .put('/v1/boosts/use')
        .set('authorization', 0)
      expect(res.status).to.equal(200);

      user = await User.findById('0');
      expect(user.isBoostActive()).to.equal(true);
      expect(user.numBoosts).to.equal(4);
      expect(user.metrics.numBoostUsed).to.equal(1);

      metrics = await BoostMetric.find();
      expect(metrics.length).to.equal(1);
      expect(metrics[0].user).to.equal('0');
      expect(metrics[0].numBoosts).to.equal(1);
      expect(metrics[0].purchaseBoostUsed).to.equal(1);

      firstExp = metrics[0].boostExpiration
      console.log('firstExp :', firstExp)

      // 2nd use
      res = await request(app)
        .put('/v1/boosts/use')
        .set('authorization', 0)
      expect(res.status).to.equal(200);

      user = await User.findById('0');
      expect(user.isBoostActive()).to.equal(true);
      expect(user.numBoosts).to.equal(3);
      expect(user.metrics.numBoostUsed).to.equal(2);

      metrics = await BoostMetric.find();
      expect(metrics.length).to.equal(1);
      expect(metrics[0].user).to.equal('0');
      expect(metrics[0].numBoosts).to.equal(2);
      expect(metrics[0].purchaseBoostUsed).to.equal(2);

      secondExp = metrics[0].boostExpiration
      console.log('secondExp :', secondExp)

      expect(secondExp).to.greaterThan(firstExp);

      // check boost transactions
      user = await User.findOne({ _id: 0 });
      user.admin = true;
      user.adminPermissions = { support: true };
      await user.save();

      res = await request(app)
        .get('/v1/admin/user/boostTransactions')
        .set('authorization', 0)
        .query({ user: '0' });
      expect(res.status).to.equal(200);
      console.log(res.body.transactions);
      expect(res.body.transactions.length).to.equal(3);
      expect(res.body.transactions[0].transactionAmount).to.equal(5);
      expect(res.body.transactions[0].newBalance).to.equal(5);
      expect(res.body.transactions[0].description).to.equal('purchased boost');
      expect(res.body.transactions[1].transactionAmount).to.equal(-1);
      expect(res.body.transactions[1].newBalance).to.equal(4);
      expect(res.body.transactions[1].description).to.equal('used boost');
      expect(res.body.transactions[2].transactionAmount).to.equal(-1);
      expect(res.body.transactions[2].newBalance).to.equal(3);
      expect(res.body.transactions[2].description).to.equal('used boost');
    })

    it('boost social proof', async () => {
      clockFake.restore();
      receipt = iapHelper.getValidAppleReceipt('boosts_5_v1', Date.now());
      res = await request(app)
        .put('/v1/boosts/purchase')
        .set('authorization', 0)
        .send({
          receipt,
          price: 3400,
          currency: 'JPY',
        });
      expect(res.status).to.equal(200);

      receipt = iapHelper.getValidAppleReceipt('boosts_10_v1', Date.now());
      res = await request(app)
        .put('/v1/boosts/purchase')
        .set('authorization', 0)
        .send({
          receipt,
          price: 4000,
          currency: 'JPY',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/social-proof/boost-purchase')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.images.length).to.equal(0);

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', 0)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      const pictureId = res.body.pictures[0];

      res = await request(app)
        .get('/v1/social-proof/boost-purchase')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.images.length).to.equal(1);
      expect(res.body.images[0]).to.equal(pictureId);
    });
  })

  describe('boost notif', () => {
    it('boosts promo notification', async () => {
      clock = sinon.useFakeTimers();

      const msPerHour = 60 * 60 * 1000;
      const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 19);

      // day 0 - no flash sale
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.50',timezone, locale: 'en' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', 0)
        .send({
          fcmToken: '0',
        });
      expect(res.status).to.equal(200);

      // no notification
      res = await request(app)
        .post('/v1/worker/boostPromoD1')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);

      // notif on day 1
      clock.tick(1 * 24 * msPerHour);

      // notification
      res = await request(app)
        .post('/v1/worker/boostPromoD1')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(1);
      expect(notifs.recent.notification.title).to.equal('Use Boost ✨');
      expect(notifs.recent.notification.body).to.equal('Get up to 4x more profile views and become the most popular soul in your area.');
      reset();
    });

    it('boosts sale notification', async () => {
      const fakeTime = DateTime.local().set({ hour: 19, minute: 0, second: 0, millisecond: 0 }).toJSDate();
      clock = sinon.useFakeTimers(fakeTime);

      const msPerHour = 60 * 60 * 1000;
      const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 19);

      const currentTime = DateTime.local();
      console.log(`Current local time: ${currentTime.toISO()}`);
      // day 0 - no flash sale
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.50',timezone, locale: 'en' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', 0)
        .send({
          fcmToken: '0',
        });
      expect(res.status).to.equal(200);

      // no notification
      res = await request(app)
        .post('/v1/worker/boostSaleD3')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(0);

      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_discount).to.equal(null);

      clock.tick(3 * 24 * msPerHour);

      // notification
      res = await request(app)
        .post('/v1/worker/boostSaleD3')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(notifs.numSent).to.equal(1);
      expect(notifs.recent.notification.title).to.equal('⚡50% OFF BOOSTS');
      expect(notifs.recent.notification.body).to.equal('Buy now to save 50% on Boosts. Limited time offer: 6 hours only.');

      res = await request(app)
        .get('/v1/config/boosts')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.boosts_discount).to.equal(50);

      reset();
    });
  })

  it('boosts no sale notification when user login before notification schedule', async () => {
    const fakeTime = DateTime.local().set({ hour: 19, minute: 0, second: 0, millisecond: 0 }).toJSDate();
    clock = sinon.useFakeTimers(fakeTime);

    const msPerHour = 60 * 60 * 1000;
    const timezone = Object.keys(ct.getAllTimezones()).find((timezone) => DateTime.local().setZone(timezone).hour == 19);

    const currentTime = DateTime.local();
    console.log(`Current local time: ${currentTime.toISO()}`);
    // day 0 - no flash sale
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.50',timezone, locale: 'en' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({
        fcmToken: '0',
      });
    expect(res.status).to.equal(200);

    // no notification
    res = await request(app)
      .post('/v1/worker/boostSaleD3')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

    res = await request(app)
      .get('/v1/config/boosts')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.boosts_discount).to.equal(null);

    clock.tick(3 * 24 * msPerHour);

    // user login before notification sent
    res = await request(app)
      .get('/v1/config/boosts')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.boosts_discount).to.equal(50);

    // no notification
    res = await request(app)
      .post('/v1/worker/boostSaleD3')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(notifs.numSent).to.equal(0);

    reset();
  });
})

it('version-dependent configs', async () => {
  basic.assignConfig.restore();
  sinon.stub(basic, 'assignConfig').returns(true);

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.94' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.95' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.96' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_229).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.96', os: 'android' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_229).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.97' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal();
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.98' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal();
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.99' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal();
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();
  expect(res.body.app_926).to.equal();
  expect(res.body.app_895).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.100' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal();
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();
  expect(res.body.app_926).to.equal();
  expect(res.body.app_895).to.equal(true);
  expect(res.body.app_893).to.equal();
  expect(res.body.app_903).to.equal();
  expect(res.body.app_904).to.equal();
  expect(res.body.app_907).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.101' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal(true);
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();
  expect(res.body.app_926).to.equal();
  expect(res.body.app_895).to.equal(true);
  expect(res.body.app_893).to.equal();
  expect(res.body.app_903).to.equal(true);
  expect(res.body.app_904).to.equal(true);
  expect(res.body.app_907).to.equal(true);
  expect(res.body.app_912).to.equal();
  expect(res.body.app_918).to.equal();
  expect(res.body.app_913).to.equal();
  expect(res.body.app_909).to.equal();
  expect(res.body.app_933).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.102' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal(true);
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();
  expect(res.body.app_926).to.equal();
  expect(res.body.app_895).to.equal(true);
  expect(res.body.app_893).to.equal();
  expect(res.body.app_903).to.equal(true);
  expect(res.body.app_904).to.equal(true);
  expect(res.body.app_907).to.equal(true);
  expect(res.body.app_912).to.equal(true);
  expect(res.body.app_918).to.equal(true);
  expect(res.body.app_913).to.equal(true);
  expect(res.body.app_909).to.equal(true);
  expect(res.body.app_933).to.equal(true);
  expect(res.body.app_786).to.equal();
  expect(res.body.app_828).to.equal();
  expect(res.body.app_935).to.equal();
  expect(res.body.app_945).to.equal();
  expect(res.body.app_916).to.equal();
  expect(res.body.app_944).to.equal();

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.103' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.app_887).to.equal(true);
  expect(res.body.app_894).to.equal();
  expect(res.body.app_229).to.equal();
  expect(res.body.app_926).to.equal();
  expect(res.body.app_895).to.equal(true);
  expect(res.body.app_893).to.equal();
  expect(res.body.app_903).to.equal(true);
  expect(res.body.app_904).to.equal(true);
  expect(res.body.app_907).to.equal(true);
  expect(res.body.app_912).to.equal(true);
  expect(res.body.app_918).to.equal(true);
  expect(res.body.app_913).to.equal(true);
  expect(res.body.app_909).to.equal(true);
  expect(res.body.app_933).to.equal(true);
  expect(res.body.app_786).to.equal(true);
  expect(res.body.app_828).to.equal(true);
  expect(res.body.app_935).to.equal(true);
  expect(res.body.app_945).to.equal(true);
  expect(res.body.app_916).to.equal(true);
  expect(res.body.app_944).to.equal(true);
});

it('app_650', async () => {
  clock = sinon.useFakeTimers();

  await Interest.insertMany([
    { interest: '#playstationgamer', name: 'playstationgamer' },
  ]);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.76', locale: 'en' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.app_650).to.equal(false);

  // add gaming interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['playstationgamer'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.app_650).to.equal(true);

  // remove gaming interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.app_650).to.equal(false);

  // add gaming interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['playstationgamer'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.app_650).to.equal(true);

  // after 14 days, app_650 should be set to false
  clock.tick(14 * 24 * 60 * 60 * 1000);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.app_650).to.equal(false);

  clock.restore();
});

it('app_515 by ip', async () => {
  sinon.stub(geoip, 'lookup').callsFake((ipAddress) => {
    return { region: ipAddress };
  });

  const testdata = [
    // region, configAssigned
    ['CA', false],
    ['TX', true],
    ['IL', true],
    ['WA', true],
  ];

  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    const region = data[0];
    const configAssigned = data[1];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({appVersion:'1.13.65'})
      .set('X-Forwarded-For', region)
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
      .set('X-Forwarded-For', region)
    expect(res.status).to.equal(200);
    expect(res.body.app_515).to.equal(configAssigned ? true : undefined);

    user = await User.findById('0');
    expect(user.ipData.region).to.equal(region);

    await mongoose.connection.db.dropDatabase();
  }
});

it('app_515 by location', async () => {
  const testdata = [
    // [latitude, longitude, configAssigned]
    [34.05, -118.24, false], // California
    [32.77, -96.79, true],   // Texas
    [41.87, -87.62, true],   // Illinois
    [47.60, -122.33, true],  // Washington
  ];

  for (let i = 0; i < testdata.length; i++) {
    await User.ensureIndexes();
    const data = testdata[i];
    const latitude = data[0];
    const longitude = data[1];
    const configAssigned = data[2];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({appVersion:'1.13.65'})
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude,
        longitude,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.app_515).to.equal(configAssigned ? true : undefined);

    await mongoose.connection.db.dropDatabase();
  }
});

it('app_511 override for derek', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  user.config.app_511 = true;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.app_511).to.equal(false);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 'V06GdMcaKhRmeJH9n0eEEogmLxJ3')
  expect(res.status).to.equal(200);

  user = await User.findById('V06GdMcaKhRmeJH9n0eEEogmLxJ3');
  user.config.app_511 = true;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 'V06GdMcaKhRmeJH9n0eEEogmLxJ3')
  expect(res.status).to.equal(200);
  expect(res.body.app_511).to.equal(true);
});

it('app_689 one-time flash sale', async () => {
  // no flash sale initially (app_688)
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.81' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale).to.equal();

  // first sale not a one-time sale, closing sale does not end sale
  res = await request(app)
    .patch('/v1/user/events')
    .set('authorization', 0)
    .send({ exitFlashSale: true });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.81' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale.discount).to.equal(50);
  expect(res.body.user.premiumFlashSale.oneTime).to.equal();

  res = await request(app)
    .patch('/v1/user/events')
    .set('authorization', 0)
    .send({ exitFlashSale: true });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.81' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale.discount).to.equal(50);
  expect(res.body.user.premiumFlashSale.oneTime).to.equal();

  // for testing purposes, change sale to one-time sale
  user = await User.findById('0');
  user.premiumFlashSaleOneTime = true;
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.81' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale.discount).to.equal(50);
  expect(res.body.user.premiumFlashSale.oneTime).to.equal(true);

  // sale should be cancelled after exiting
  res = await request(app)
    .patch('/v1/user/events')
    .set('authorization', 0)
    .send({ exitFlashSale: true });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.81' })
  expect(res.status).to.equal(200);
  expect(res.body.user.premiumFlashSale).to.equal();
});

it('use_college by ip', async () => {
  sinon.stub(geoip, 'lookup').callsFake((ipAddress) => {
    if (ipAddress == null || ipAddress == 'null') {
      return { };
    }
    return { country: ipAddress };
  });

  const testdata = [
    // country, configAssigned
    [null, false],
    ['US', false],
    ['GB', true],
    ['AU', true],
    ['NZ', true],
  ];

  for (let i = 0; i < testdata.length; i++) {
    const data = testdata[i];
    const region = data[0];
    const configAssigned = data[1];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .set('X-Forwarded-For', region)
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
      .set('X-Forwarded-For', region)
    expect(res.status).to.equal(200);
    expect(res.body.use_college).to.equal(configAssigned);

    await mongoose.connection.db.dropDatabase();
  }
});

it('use_college by location', async () => {
  const testdata = [
    // [latitude, longitude, configAssigned]
    [null, null, false],
    [40.7, -74.0, false], // NYC
    [51.5, 0, true],   // London
    [-33.8, 151.2, true],   // Sydney
    [-36.8, 174.7, true],  // Auckland
  ];

  for (let i = 0; i < testdata.length; i++) {
    await User.ensureIndexes();
    const data = testdata[i];
    const latitude = data[0];
    const longitude = data[1];
    const configAssigned = data[2];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);

    if (latitude !== null) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', 0)
        .send({
          latitude,
          longitude,
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.use_college).to.equal(configAssigned);

    await mongoose.connection.db.dropDatabase();
  }
});

it('use_college by timezone', async () => {
  const testdata = [
    // [timezone, configAssigned]
    [null, false],
    ['America/Los_Angeles', false], // US
    ['Europe/London', true],   // UK
    ['Australia/Sydney', true],   // AU
    ['Pacific/Auckland', true],  // NZ
  ];

  for (let i = 0; i < testdata.length; i++) {
    await User.ensureIndexes();
    const data = testdata[i];
    const timezone = data[0];
    const configAssigned = data[1];
    console.log(i, data);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({timezone})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.use_college).to.equal(configAssigned);

    await mongoose.connection.db.dropDatabase();
  }
});

it('show config show_message_sort_options and app_687', async () => {
    // first create boo bot account
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    clock = sinon.useFakeTimers(new Date('2025-07-08'));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.53' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.show_message_sort_options).to.equal(true)
    expect(res.body.app_687).to.equal(false)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.86' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.show_message_sort_options).to.equal(true)
    expect(res.body.app_687).to.equal(true)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.86' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.show_message_sort_options).to.equal(true)
    expect(res.body.app_687).to.equal(true)

    clock.restore()

    let user = await User.findOne({ _id: '1' })
    user.createdAt = new Date('2022-11-11')
    await user.save()

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.show_message_sort_options).to.equal(undefined)
    expect(res.body.app_687).to.equal(false)

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.86' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.show_message_sort_options).to.equal(true)
    expect(res.body.app_687).to.equal(true)
});

it('app_803', async () => {
  // user 0 premium
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  user.premiumExpiration = Date.now() + 86400000;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.send_likes_verified_only).to.equal(true);

  // user 1 not premium
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.send_likes_verified_only).to.equal();
});

it('use_storekit1', async () => {
  // version at least 1.13.98 -> use_storekit1: false
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.98' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(false);

  // version less than 1.13.98 -> use_storekit1: true
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.97' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(true);

  // allow config to be manually set
  user = await User.findById('0');
  user.config.use_storekit1 = true;
  await user.save();
  user = await User.findById('1');
  user.config.use_storekit1 = false;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(true);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(false);

  user = await User.findById('0');
  user.config.use_storekit1 = false;
  await user.save();
  user = await User.findById('1');
  user.config.use_storekit1 = true;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(false);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(true);

  user = await User.findById('0');
  user.config.use_storekit1 = undefined;
  await user.save();
  user = await User.findById('1');
  user.config.use_storekit1 = undefined;
  await user.save();

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(false);

  res = await request(app)
    .get('/v1/config')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.use_storekit1).to.equal(true);
});
