const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const { exportChats } = require('../lib/process-chat-export-requests');
const { exportData } = require('../lib/process-data-request');
const { error } = require('../lib/error');

module.exports = function () {
  router.post(
    '/',
    asyncHandler(async (req, res, next) => {
      if (req.body.route === 'exportChats') {
        await exportChats(req, res, next);
      }
      if (req.body.route === 'exportData') {
        await exportData(req, res, next);
      }
      if (req.body.route === 'error'){
        await error(req, res, next);
      }
    }),
  );

  router.post('/exportChats', asyncHandler(exportChats));
  router.post('/exportData', asyncHandler(exportData));
  router.post('/error', asyncHandler(error));

  return router;
};
