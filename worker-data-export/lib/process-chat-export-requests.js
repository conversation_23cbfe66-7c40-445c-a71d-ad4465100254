const path = require('path');
const cfsign = require('aws-cloudfront-sign');
const { getSigningParams } = require('../../lib/cloudfront');
const { s3, AWS_S3_BUCKET } = require('../../lib/s3');
const { sendEmailTemplateSES, translateEmail } = require('../../lib/email');
const User = require('../../models/user');
const Chat = require('../../models/chat');
const Message = require('../../models/message');
const ChatExportHistory = require('../../models/chat-export-history');
const { sendAutomatedReply } = require('../../lib/chat');
const { stringify } = require('flatted');
const { serializeError } = require('serialize-error');
const archiver = require('archiver');
const { PassThrough, Readable } = require('stream');

const log = (msg) => console.log(`[chat-export-requests]: ${msg}`);

const config = {
  retryLimit: 3,
  bucketName: process.env.AWS_EXPIRY_S3_BUCKET || 'MOCK_S3_EXPIRY_BUCKET',
};

let chatExportRunning = false;

const sendEmail = async (user, otherName, signedUrl) => {
  const template = 'chat-download-v1';
  const emailContent = [
    {
      name: 'GREETINGS',
      content: translateEmail({ phrase: 'Hello {{name}}', locale: user.locale }, { name: user.firstName }),
    },
    {
      name: 'MESSAGE',
      content: translateEmail({
        phrase: `We have compiled all the information associated with your conversation with {{other_name}}. You can download it using the link below, valid for 7 days. If you have any questions, please reach out to us.`,
        locale: user.locale,
      }, { other_name: otherName }),
    },
    {
      name: 'DOWNLOAD_NOW',
      content: translateEmail({ phrase: 'Download Data', locale: user.locale }),
    },
    {
      name: 'DOWNLOAD_LINK',
      content: signedUrl,
    },
    {
      name: 'THANK_YOU',
      content: translateEmail({ phrase: 'Love,', locale: user.locale }),
    },
    {
      name: 'TEAM_BOO',
      content: translateEmail({ phrase: 'The Boo Team', locale: user.locale }),
    },
  ];
  if (user.versionAtLeast('1.13.64')) {
    const dataDownloadLink = user.versionAtLeast('1.13.73') ? `[Download Data](${signedUrl})` : signedUrl;
    const messageText = `${translateEmail({ phrase: `We have compiled all the information associated with your conversation with {{other_name}}. You can download it using the link below, valid for 7 days.`, locale: user.locale }, { other_name: otherName })}\n\n${dataDownloadLink}`;
    await sendAutomatedReply(user, messageText);
  } else {
    await sendEmailTemplateSES(user, template, 'Your Chat Export is Now Available', emailContent);
  }
};

const getMessages = async (chat) =>
  await Message.aggregate([
    {
      $match: {
        chat: chat._id,
      },
    },
    {
      $sort: {
        createdAt: 1,
      },
    },
    {
      $project: {
        _id: 0,
        createdAt: 1,
        sender: 1,
        text: 1,
        gif: 1,
        image: 1,
        audio: 1,
        video: 1,
        unsent: 1,
      },
    },
  ]);

const appendMediaFilesToArchive = async (archive, mediaFiles) => {
  for (const file of mediaFiles) {
    try {
      await s3.headObject({ Bucket: file.Bucket, Key: file.Key }).promise();
      const s3Stream = s3.getObject({ Bucket: file.Bucket, Key: file.Key }).createReadStream();
      archive.append(s3Stream, { name: file.zipPath });
    } catch (err) {
      log(`chatId ${file.chatId}: Failed uploading media: ${file.Key} with error: ${err}`);
      if (file.parent && file.field) {
        file.parent[file.field] = 'Not found';
      }
    }
  }
};

const streamZipToS3 = async ({ chatId, mediaFiles, messages, outputKey }) => {
  const passThrough = new PassThrough();

  const uploadPromise = s3
    .upload({
      Bucket: config.bucketName,
      Key: outputKey,
      Body: passThrough,
      ContentType: 'application/zip',
    })
    .promise();

  const archive = archiver('zip', { zlib: { level: 9 } });

  archive.on('warning', (err) => {
    log(`chatId ${chatId}: AppendMediaFilesToArchive warning: ${err.message}`);
  });

  archive.on('error', (err) => {
    log(`chatId ${chatId}: AppendMediaFilesToArchive error: ${err.message}`);
    throw err;
  });

  archive.pipe(passThrough);
  await appendMediaFilesToArchive(archive, mediaFiles);

  archive.append(Readable.from([JSON.stringify(messages, null, 2)]), {
    name: `messages.json`,
  });

  await new Promise((resolve, reject) => {
    archive.on('end', resolve);
    archive.on('error', reject);
    archive.finalize();
  });

  await uploadPromise;
};

const addMediaFile = ({ mediaFiles, parent, field, index, chatId }) => {
  const value = parent[field];
  if (!value || typeof value !== 'string') return;

  const ext = path.extname(value) || '';
  const zipPath = `media/${index}${ext}`;

  mediaFiles.push({
    Bucket: AWS_S3_BUCKET,
    Key: value,
    zipPath,
    parent,
    field,
    chatId,
  });

  parent[field] = zipPath;
};

const processPendingRequest = async (dataRequest) => {
  const startTime = Date.now();
  try {
    let chat = await Chat.findById(dataRequest.chatId);
    if (chat && !chat.deletedAt) {
      const mediaFiles = [];

      const messages = await getMessages(chat);
      log(`chatId ${dataRequest.chatId}: Found ${messages.length} messages`);

      const USER_1 = await User.findById(chat.users[0]);
      const USER_2 = await User.findById(chat.users[1]);

      for (let j = 0; j < messages.length; j++) {
        if (messages[j].sender === USER_1._id) {
          messages[j].sender = USER_1.firstName;
        } else if (messages[j].sender === USER_2.id) {
          messages[j].sender = USER_2.firstName;
        } else {
          log(`chatId ${dataRequest.chatId}: Message sender does not match either user!`);
        }

        messages[j].text = messages[j].text.replace('[update to latest version to view]', '').trim();

        if (messages[j].unsent) {
          Object.assign(messages[j], {
            text: undefined,
            gif: undefined,
            image: undefined,
            audio: undefined,
            video: undefined,
          });
        }
        if (messages[j].image) {
          addMediaFile({ mediaFiles, parent: messages[j], field: 'image', index: j, chatId: dataRequest.chatId });
        }
        if (messages[j].audio) {
          addMediaFile({ mediaFiles, parent: messages[j], field: 'audio', index: j, chatId: dataRequest.chatId });
        }
        if (messages[j].video) {
          addMediaFile({ mediaFiles, parent: messages[j], field: 'video', index: j, chatId: dataRequest.chatId });
        }
      }

      const outputKey = `data-export/chat-${Date.now()}.zip`;
      await streamZipToS3({
        chatId: dataRequest.chatId,
        mediaFiles,
        messages,
        outputKey,
      });

      let signedUrl = cfsign.getSignedUrl(
        `${process.env.WEB_DOMAIN}/${outputKey}`,
        getSigningParams(7),
      );

      if (process.env.TESTING) {
        signedUrl = 'MOCK_SIGNED_URL';
      }

      await sendEmail(USER_1, USER_2.firstName, signedUrl);
      await sendEmail(USER_2, USER_1.firstName, signedUrl);

      dataRequest.status = 'completed';
      dataRequest.downloadKey = outputKey;
      dataRequest.emailedAt = new Date();
    } else {
      log(`chatId ${dataRequest.chatId}: Could not find chat or chat deleted`);
      dataRequest.status = 'completed';
    }
  } catch (error) {
    log(`chatId ${dataRequest.chatId}: Error while exporting data: ${error.message}`);

    dataRequest.retryCount += 1;
    dataRequest.status = dataRequest.retryCount >= config.retryLimit ? 'failed' : dataRequest.status;
    if (dataRequest.status === 'failed' || process.env.TESTING) {
      dataRequest.error = stringify(serializeError(error));
      dataRequest.errorAt = new Date();
    }
  } finally {
    const durationSec = Number(((Date.now() - startTime) / 1000).toFixed(2));
    dataRequest.processingTimeSec = durationSec;
    await dataRequest.save();
    log(`chatId ${dataRequest.chatId}: Chat export ${dataRequest.status}, retry count ${dataRequest.retryCount} in ${durationSec} seconds`);
  }
};

const exportChats = async (req, res, next) => {
  // always return 200 immediately: worker environment in AWS Elastic Beanstalk has a timeout
  if (!process.env.TESTING) {
    res.json({});
  }

  if (chatExportRunning) {
    log('Chat export job already running, skipping this job');
    return;
  }

  chatExportRunning = true;
  try {
    const approvedRequests = await ChatExportHistory.find({ status: 'approved' });
    for (const exportRequest of approvedRequests) {
      await processPendingRequest(exportRequest);
    }
  } catch (error) {
    log(`Fatal error exporting chat: ${error.message}`);
  } finally {
    chatExportRunning = false;
    if (process.env.TESTING) {
      // for test environments, return at end
      res.json({});
    }
  }
};

module.exports = { exportChats };
