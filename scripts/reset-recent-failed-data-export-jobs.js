// ENV REQUIRED: MONGODB_URI_RECORDS
const mongoose = require('mongoose');
const DataRequestHistory = require('../models/data-request-history');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    const result = await DataRequestHistory.updateMany(
      {
        status: 'failed',
        requestedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
      },
      {
        $set: { status: 'pending' },
      },
    );

    console.log(`Matched: ${result.matchedCount}, Modified: ${result.modifiedCount}`);
  } catch (error) {
    console.log(`Error updating records: ${error}`);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
})();
