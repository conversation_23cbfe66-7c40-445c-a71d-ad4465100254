/* eslint-disable no-continue */
const fs = require('fs');
const path = require('path');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');
const { OpenAI } = require('../lib/prompt');
const openaiClient = require('../lib/openai-client');
const { mapLanguageCodeToName } = require('../lib/languages');

const TERMS_PATH = '/Users/<USER>/Downloads/terms.txt'; // Path to translated terms of service markdown file
const RULES_PATH = '/Users/<USER>/Downloads/Ban Notice Rules - Sheet1.csv'; // Path to CSV file with policy rules
const LOCALES_DIR = path.join(__dirname, '../lib/locales-legals');

const client = openaiClient.getOpenaiClient();
const openai = new OpenAI(client, 'gpt-4o-mini');

const loadTerms = async () => {
  if (!fs.existsSync(TERMS_PATH)) throw new Error(`TERMS_PATH not found: ${TERMS_PATH}`);
  const content = await fs.promises.readFile(TERMS_PATH, 'utf8');
  const data = JSON.parse(content);
  const terms = {};
  const languageCodes = Object.keys(data);
  for (const code of languageCodes) {
    terms[code] = data[code].markdown;
  }
  return { languageCodes, terms };
};

const loadRules = async () => {
  if (!fs.existsSync(RULES_PATH)) throw new Error(`RULES_PATH not found: ${RULES_PATH}`);
  const data = await fs.promises.readFile(RULES_PATH, 'utf8');
  const lines = parse(data, {
    delimiter: ',',
    columns: true,
    skip_empty_lines: true,
    relax_quotes: true,
  });

  const items = new Set();
  for (const line of lines) {
    if (line.Policy) items.add(line.Policy);
    if (line.Section) items.add(line.Section);
    if (line.Rule) items.add(line.Rule);
  }

  return [...items].filter(Boolean);
};

const preparePrompt = (terms, locale, string) => {
  const language = mapLanguageCodeToName(locale);
  return `
Here is the English version of our terms of service:
${terms.en}

Here is the ${language} version of our terms of service:
${terms[locale]}

Please identify the translated string that corresponds to: 
"${string}"

Respond in the following JSON format only:
{
  "original": "${string}",
  "translation": "[${language} translation here]"
}
  `.trim();
};

const translateItem = async (item, locale, terms) => {
  const prompt = preparePrompt(terms, locale, item);

  for (let attempt = 1; attempt <= 2; attempt++) {
    try {
      const response = await openai.executePrompt({
        prompt,
        temperature: 0.0,
        response_format: { type: 'json_object' },
      });

      const output = JSON.parse(response?.output);
      if (!output?.translation) throw new Error('Missing "translation" field');
      return output.translation;
    } catch (err) {
      const status = attempt === 1 ? 'First attempt failed' : 'Failed after retry';
      console.log(`${status} for "${item}" [${locale}]: ${err.message}`);
      if (attempt === 2) return null;
    }
  }
};

const translateBatch = async (items, locale, terms) => {
  const result = {};
  const promises = items.map(async (item) => {
    const translation = await translateItem(item, locale, terms);
    if (translation) result[item] = translation;
  });

  await Promise.all(promises);
  return result;
};

const ensureLocaleFile = async (locale) => {
  const filePath = path.join(LOCALES_DIR, `${locale}.json`);
  if (!fs.existsSync(filePath)) {
    await fs.promises.writeFile(filePath, JSON.stringify({}));
    return {};
  }
  const content = await fs.promises.readFile(filePath, 'utf8');
  return JSON.parse(content);
};

const writeLocaleFile = async (locale, data) => {
  const filePath = path.join(LOCALES_DIR, `${locale}.json`);
  await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
  console.log(`Saved translations for "${locale}" to: ${filePath}`);
};

(async () => {
  try {
    const { languageCodes, terms } = await loadTerms();
    const rules = await loadRules();

    for (const locale of languageCodes) {
      console.log(`Processing locale: ${locale}`);
      const translations = await ensureLocaleFile(locale);
      const untranslatedItems = rules.filter((item) => !translations[item]);

      if (untranslatedItems.length === 0) {
        console.log(`No new items to translate for "${locale}"`);
        continue;
      }

      if (locale === 'en') {
        untranslatedItems.forEach((item) => {
          translations[item] = item;
        });
      } else {
        const newTranslations = await translateBatch(untranslatedItems, locale, terms);
        Object.assign(translations, newTranslations);
      }

      await writeLocaleFile(locale, translations);
    }

    console.log('All locales processed successfully!');
  } catch (err) {
    console.log('Fatal error occurred:', err);
    process.exit(1);
  }
})();
