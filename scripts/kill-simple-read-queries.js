const { MongoClient } = require('mongodb');

// --- configure me ---
const DB_NAME = 'test';
const COLL_NAME = 'users';
// ---------------------

const uri = process.env.MONGODB_URI;
const ns = `${DB_NAME}.${COLL_NAME}`;

(async () => {
  const client = new MongoClient(uri, { serverSelectionTimeoutMS: 15000 });
  await client.connect();
  const adminDb = client.db('admin');

  // Server-side filtering only:
  // - active ops on our namespace
  // - running >= 300 seconds
  // - command.find === COLL_NAME (simple read queries)
  // - command.filter is an object, has exactly one key
  // - that single key is 'stripePaymentFingerprints' whose value is a string
  const pipeline = [
    { $currentOp: { allUsers: true, localOps: true } },
    {
      $match: {
        active: true,
        ns,
        secs_running: { $gte: 300 },
        'command.find': COLL_NAME,
        'command.filter': { $type: 'object' },
        'command.filter.stripePaymentFingerprints': { $type: 'string' },
      },
    },
    {
      // ensure the filter is EXACTLY { stripePaymentFingerprints: <string> }
      $match: {
        $expr: {
          $eq: [{ $size: { $objectToArray: '$command.filter' } }, 1],
        },
      },
    },
    {
      $project: {
        opid: 1,
        ns: 1,
        secs_running: 1,
        client: 1,
        planSummary: 1,
        command: 1,
      },
    },
  ];

  const ops = await adminDb.aggregate(pipeline).toArray();

  if (ops.length === 0) {
    console.log('No matching long-running ops found.');
    await client.close();
    return;
  }

  console.log(`About to kill these ${ops.length} operations:`);
  for (const op of ops) {
    console.log({
      opid: op.opid,
      ns: op.ns,
      secs_running: op.secs_running,
      client: op.client,
      planSummary: op.planSummary,
      filter: op.command?.filter,
    });
  }

  if (process.env.KILL) {
    for (const op of ops) {
      try {
        const res = await adminDb.command({ killOp: 1, op: op.opid });
        console.log(`killOp(${op.opid}) ->`, res);
      } catch (e) {
        console.warn(`Failed to kill ${op.opid}: ${e.message}`);
      }
    }
  }

  await client.close();
})().catch((e) => {
  console.error(e);
  process.exit(1);
});

